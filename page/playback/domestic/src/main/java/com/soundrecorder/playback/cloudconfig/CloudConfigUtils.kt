/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  CloudConfigUtils
 * * Description :
 * * Version     : 1.0
 * * Date        : 2021/10/1
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.cloudconfig

import com.oplus.nearx.cloudconfig.CloudConfigCtrl
import com.oplus.nearx.cloudconfig.Env
import com.oplus.nearx.cloudconfig.api.AreaCode
import com.oplus.nearx.cloudconfig.api.FileService
import com.oplus.nearx.cloudconfig.device.readFile
import com.oplus.nearx.cloudconfig.observable.Scheduler
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.OS12FeatureUtil
import org.json.JSONObject
import java.io.File

object CloudConfigUtils {
    var isSupportWpsExport = true
    private var hasUpdateConvertConfig = false
    private const val TAG = "CloudConfigUtils"
    private const val SOUND_RECORDER_CONFIG_NAME = "recorder_convert_config"
    private const val PRODUCT_ID = "mdp_1725"
    private val cloudConfig by lazy(mode = LazyThreadSafetyMode.NONE) {
        CloudConfigCtrl.Builder()
            .apiEnv(Env.RELEASE)
            .productId(PRODUCT_ID)
            .areaCode(AreaCode.CN)
            .build(BaseApplication.getAppContext())
    }

    fun updateConvertConfig(forceRefresh: Boolean = false) {
        if (OS12FeatureUtil.isSuperSoundRecorderEpicEffective() && (forceRefresh || !hasUpdateConvertConfig)) {
            cloudConfig.create(FileService::class.java)
                .observeFile(SOUND_RECORDER_CONFIG_NAME)
                .observeOn(Scheduler.io())
                .map { file: File ->
                    val configStringBuilder = readFile(file.absolutePath, "utf-8")
                    DebugUtil.d(TAG, "updateRecorderConfig sb=$configStringBuilder")
                    try {
                        val json = JSONObject(configStringBuilder.toString())
                        isSupportWpsExport = (json.optInt("isSupportWpsExport") != 0)
                    } catch (e: java.lang.Exception) {
                        DebugUtil.d(TAG, "updateRecorderConfig parse error: $e")
                    }
                    isSupportWpsExport
                }.subscribeOn(Scheduler.main()).subscribe { }
            hasUpdateConvertConfig = true
        }
    }
}