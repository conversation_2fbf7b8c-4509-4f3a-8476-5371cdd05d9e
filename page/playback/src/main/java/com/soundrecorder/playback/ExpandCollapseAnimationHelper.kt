/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: ExpandCollapseAnimationHelper
 Description:
 Version: 1.0
 Date: 2025/07/11
 Author: W9078961
 -----------Revision History-----------
 <author> <date> <version> <desc>
 */

package com.soundrecorder.playback

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.content.Context
import android.graphics.drawable.GradientDrawable
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.animation.addListener
import androidx.core.animation.doOnEnd
import androidx.core.animation.doOnStart
import androidx.core.view.isVisible
import androidx.core.view.marginBottom
import androidx.core.view.marginLeft
import androidx.core.view.marginRight
import androidx.core.view.marginTop
import androidx.core.view.updateLayoutParams
import androidx.dynamicanimation.animation.FloatPropertyCompat
import com.coui.appcompat.animation.COUIMoveEaseInterpolator
import com.coui.appcompat.animation.dynamicanimation.COUIDynamicAnimation
import com.coui.appcompat.animation.dynamicanimation.COUISpringAnimation
import com.coui.appcompat.animation.dynamicanimation.COUISpringForce
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.uiutil.ShadowUtils
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.utils.gone
import com.soundrecorder.common.utils.visible
import com.soundrecorder.playback.databinding.FragmentPlaybackContainerBinding
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlin.math.abs

class ExpandCollapseAnimationHelper(
    private val context: Context,
    private val mViewModel: PlaybackContainerViewModel?,
    private val binding: FragmentPlaybackContainerBinding
) {

    init {
        initLayout()
        initListener()
    }

    companion object {
        private const val TAG: String = "ExpandCollapseAnimationHelper"
        private const val DEFAULT_SPRING_BOUNCE: Float = 0f
        private const val DEFAULT_SPRING__RESPONSE: Float = 0.4f
        private const val SEEK_TIME_SPRING__RESPONSE: Float = 0.2f
        private const val PANEL_SPRING_RECOVER_RESPONSE: Float = 0.3f
        private const val MARKLIST_SIZE_SPRING_RESPONSE: Float = 0.1f
        private const val FORWARD_BACKWARD_DELAYED_TIME: Long = 117L
        private const val FORWARD_BACKWARD_REVERSE_TIME: Long = 100L
        private const val RESPONSE_MARK_LIST_ACTION_ANIMATION: Float = 0.45f
        private const val MARK_LIST_ACTION_TIME: Long = 100L
        private const val TIME_SPRING__RESPONSE: Float = 0.3f
        private const val TIME_DELAYED_TIME: Long = 233L
        private const val WAVE_FORM_BG_SPRING__RESPONSE: Float = 0.2f
        private const val WAVE_FORM_BG_RADIUS_SPRING__RESPONSE: Float = 0.14f
        private const val WAVE_FORM_BG_DELAYED_TIME: Long = 50L
        private const val RADIUS_NORMAL: Float = 100f //面板默认圆角值
        const val RADIUS_ANIMATION: Float = 72f //面板展开后的圆角值
        private const val WAVEFORM_RADIUS_NORMAL: Float = 96f //波形背景默认圆角值
        private const val PROPERTY_ALPHA_KEY: String = "alpha"
        private const val PROPERTY_ALPHA_FULL: Float = 1.0f
        private const val PROPERTY_ALPHA_TRANSPARENT: Float = 0.0f
    }

    //播放按钮控制
    private var playScaleAnimation: COUISpringAnimation? = null //播放按钮缩放动画
    private var circleRadiusScaleAnimation: COUISpringAnimation? = null //圆形按钮的radius缩放动画
    private var playButtonNormal: Float = 1f //播放按钮默认尺寸
    private var playTranslationX: COUISpringAnimation? = null //播放按钮X平移动画
    private var shadowTranslationX: COUISpringAnimation? = null //按钮阴影X平移动画
    private var playTranslationY: COUISpringAnimation? = null  //播放按钮Y平移动画 下边距
    private var shadowTranslationY: COUISpringAnimation? = null //按钮阴影Y平移动画
    private var distancePlayXNormal: Float = 1f  //播放按钮X轴默认距离
    private var distancePlayX: Float = 1f  //播放按钮X轴移动距离
    private var distancePlayY: Float = 1f  //播放按钮X轴移动距离
    private var distancePlayYNormal: Float = 1f  //播放按钮Y轴默认距离
    private var shadowAlphaAnimation: ObjectAnimator? = null
    private var shadowAlphaRecoverAnimation: ObjectAnimator? = null

    //播放面板展开动画
    private var panelAnimator: COUISpringAnimation? = null
    private var panelHeightNormal: Float = 0f //面板默认高度
    private var radiusAnimator: COUISpringAnimation? = null //面板圆角值改变动画

    //快进、快退、旗标
    private var forwardAlphaAnimation: COUISpringAnimation? = null //快进透明动画
    private var forwardRecoverAlphaAnimation: ObjectAnimator? = null //快进恢复透明动画
    private var forwardTranslationXAnimation: COUISpringAnimation? = null //快进X偏移动画
    private var forwardTranslationX: Float = 1f  //快进按钮X轴滚动距离
    private var forwardTranslationXNormal: Float = 1f  //快进按钮X轴默认距离
    private var forwardTranslationYAnimation: COUISpringAnimation? = null //快进Y偏移动画
    private var backwardAlphaAnimation: COUISpringAnimation? = null //快退透明动画
    private var backwardRecoverAlphaAnimation: ObjectAnimator? = null //快退恢复透明动画
    private var backwardTranslationXAnimation: COUISpringAnimation? = null //快退X偏移动画
    private var backwardTranslationX: Float = 1f  //快退按钮X轴滚动距离
    private var backwardTranslationXNormal: Float = 1f  //快退按钮X轴默认距离
    private var backwardTranslationYAnimation: COUISpringAnimation? = null //快退Y偏移动画
    private var markAddAlphaAnimation: COUISpringAnimation? = null //标记透明动画
    private var markAddRecoverAlphaAnimation: ObjectAnimator? = null //标记恢复透明动画
    private var markAddTranslationXAnimation: COUISpringAnimation? = null //标记按钮X偏移动画
    private var markAddTranslationX: Float = 1f  //标记按钮X轴滚动距离
    private var markAddTranslationXNormal: Float = 1f  //标记按钮X轴默认距离
    private var markAddTranslationYAnimation: COUISpringAnimation? = null //标记按钮Y偏移动画
    private var forwardAndBackDelayJob: Job? = null //延时任务执行透明动画
    private var animatorSet: AnimatorSet? = null
    private var hasStarted: Boolean = false
    private var buttonTranslationY: Float = 1f  //快进、快退、标记按钮Y轴滚动距离
    private var buttonTranslationYNormal: Float = 1f  //快进、快退、标记按钮Y轴默认距离

    //标记列表按钮
    private var markListTranslationX: COUISpringAnimation? = null //标记列表X平移动画
    private var markListTranslationY: COUISpringAnimation? = null  //标记列表Y平移动画 下边距
    private var markListScaleAnimation: COUISpringAnimation? = null //标记列表宽高改变动画
    private var distanceMarkListXNormal: Float = 0f  //标记列表X轴默认距离
    private var distanceMarkListX: Float = 0f  //标记列表X轴移动距离
    private var distanceMarkListY: Float = 0f  //标记列表Y轴移动距离
    private var distanceMarkListYNormal: Float = 0f  //标记列表Y轴默认距离
    private var markListAlphaAnimation: ObjectAnimator? = null  //标记列表背景透明动画
    private var markListRecoverAlphaAnimation: COUISpringAnimation? = null  //标记列表背景显示动画

    //总时间、当前时间、seek时间、把手
    private var currentTimeAlphaAnimation: COUISpringAnimation? = null //当前时间透明动画
    private var durationTimeAlphaAnimation: COUISpringAnimation? = null //总时间透明动画
    private var currentTimeRecoverAlphaAnimation: ObjectAnimator? = null //当前时间恢复透明动画
    private var durationTimeRecoverAlphaAnimation: ObjectAnimator? = null //总时间恢复透明动画
    private var seekTimeAlphaAnimation: COUISpringAnimation? = null //seekTime透明动画
    private var seekTimeTranslationY: COUISpringAnimation? = null  //seekTimeY平移动画
    private var distanceSeekTimeNormalY: Float = 0f //seekTimeY轴默认距离
    private var distanceSeekTimeY: Float = 0f //seekTimeY轴移动距离
    private var seekTimeRecoverAlphaAnimation: ObjectAnimator? = null //seekTime恢复透明动画
    private var closeHandleAlphaAnimation: COUISpringAnimation? = null //把手透明动画
    private var closeHandRecoverAlphaAnimation: ObjectAnimator? = null //把手恢复透明动画
    private var timeTextActionDelayJob: Job? = null
    private var timeHasStarted: Boolean = false

    //波形图背景
    private var waveFormBgTranslationY: COUISpringAnimation? = null  //波形图背景Y平移动画
    private var waveFormBgHeightAnimation: COUISpringAnimation? = null //波形图背景高度改变动画
    private var waveFormBgWidthAnimation: COUISpringAnimation? = null //波形图背景宽度改变动画
    private var waveFormBgRadiusAnimator: COUISpringAnimation? = null //波形图背景圆角值改变动画
    private var distanceWaveFormY: Float = 0f  //波形图背景Y轴移动距离
    private var distanceWaveFormYNormal: Float = 0f  //波形图背景默认Y
    private var waveFormBgHeightNormal: Float = 0f  //波形图背景默认高度
    private var waveFormBgWidthNormal: Float = 0f  //波形图背景默认宽度
    private var waveFormBgDelayJob: Job? = null

    var isRotate: Boolean = false  //是否旋转

    //展开状态下播放按钮的大小
    val expandPlayButtonSize: Float by lazy {
        context.resources.getDimension(com.soundrecorder.common.R.dimen.circle_record_button_diam)
    }

    //展开状态下播放按钮ICON的circle_radius大小
    val expandPlayButtonIconSize: Float by lazy {
        context.resources.getDimension(com.soundrecorder.common.R.dimen.circle_record_button_radius)
    }

    //折叠状态下播放按钮ICON的circle_radius大小
    private val playButtonIconNormal: Float by lazy {
        context.resources.getDimension(R.dimen.play_button_icon_normal)
    }

    //展开状态下播放按钮的下边距
    val playbackMarginBottom: Float by lazy {
        context.resources.getDimension(R.dimen.circle_playback_button_margin_bottom)
    }

    //折叠状态下播放按钮的下边距
    private val playButtonNormalBottom: Float by lazy {
        context.resources.getDimension(R.dimen.play_btn_marginBottom)
    }

    //折叠状态下播放按钮的End边距
    private val playButtonNormalEndMargin: Float by lazy {
        context.resources.getDimension(R.dimen.play_btn_marginEnd)
    }

    //展开状态下背景面板高度
    val panelExpandHeight: Float by lazy {
        context.resources.getDimension(R.dimen.panel_height)
    }

    //展开状态下背景面板弧度
    private val panelExpandRadius: Float by lazy {
        context.resources.getDimension(R.dimen.panel_expand_radius)
    }

    //标记列表左边距
    val markListMarginLeft: Float by lazy {
        context.resources.getDimension(R.dimen.mark_list_margin_left)
    }

    //标记列表下边距
    val markListMarginBottom: Float by lazy {
        context.resources.getDimension(R.dimen.mark_list_margin_bottom)
    }

    //展开状态下标记列的大小
    val markListSize: Float by lazy {
        context.resources.getDimension(R.dimen.mark_ist_size)
    }

    //展开状态下标记列的大小
    private val markListSizeNormal: Float by lazy {
        context.resources.getDimension(R.dimen.mark_ist_size_normal)
    }

    //展开状态下标记列图片的大小
    val markListParentSize: Float by lazy {
        context.resources.getDimension(R.dimen.mark_list_parent_size)
    }

    //展开状态下波开图片距离下边距
    val waveFormMarginBottom: Float by lazy {
        context.resources.getDimension(R.dimen.wave_form_margin_bottom)
    }

    //展开状态下波开图片高度
    val waveFormExpandHeight: Float by lazy {
        context.resources.getDimension(R.dimen.wave_form_expand_height)
    }

    //seekTime Y轴偏移量
    val seekTimeY: Float by lazy {
        context.resources.getDimension(R.dimen.seek_time_translation_y)
    }

    //快进、快退、标记按钮 X Y 轴偏移量
    val buttonDistance: Float by lazy {
        context.resources.getDimension(R.dimen.backward_forward_translation)
    }

    //波形图展开后背景颜色更新
    private val waveFormBgRadiusEndListener =
        COUIDynamicAnimation.OnAnimationEndListener { animation, canceled, value, velocity ->
            val drawable = binding.floatButtonPanel.imgWaveFormBg.background as GradientDrawable
            drawable.setColor(ContextCompat.getColor(context, R.color.wave_form_expand_bg_color))
        }

    //波形图折叠后背景颜色更新
    private val waveFormBgRecoverRadiusEndListener =
        COUIDynamicAnimation.OnAnimationEndListener { animation, canceled, value, velocity ->
            val drawable = binding.floatButtonPanel.imgWaveFormBg.background as GradientDrawable
            drawable.setColor(ContextCompat.getColor(context, R.color.wave_form_bg_color))
        }

    //透明度动画开始监听(Spring动画没有onStart方法，通过更新动画间接实现)
    private val alphaAnimationUpdateListener =
        COUIDynamicAnimation.OnAnimationUpdateListener { animation, canceled, value ->
            if (!hasStarted) {
                hasStarted = true
                binding.floatButtonPanel.floatImgForward.isVisible = true
                binding.floatButtonPanel.floatImgBackward.isVisible = true
                binding.floatButtonPanel.floatImgMarkAdd.isVisible = true
            }
        }

    //透明度动画开始监听(Spring动画没有onStart方法，通过更新动画间接实现)
    private val timeAlphaAnimationUpdateListener =
        COUIDynamicAnimation.OnAnimationUpdateListener { animation, canceled, value ->
            if (!timeHasStarted) {
                timeHasStarted = true
                binding.floatButtonPanel.floatTvCurrent.isVisible = true
                binding.floatButtonPanel.floatTvDuration.isVisible = true
                binding.floatButtonPanel.tvSeekTime.isVisible = true
                binding.floatButtonPanel.imgCloseHandle.isVisible = true
            }
        }

    //回退透明度动画结束监听
    private val reverseAlphaAnimationListener = object : AnimatorListenerAdapter() {
        override fun onAnimationEnd(animation: Animator) {
            hasStarted = false
            binding.floatButtonPanel.floatImgForward.isVisible = false
            binding.floatButtonPanel.floatImgBackward.isVisible = false
            binding.floatButtonPanel.floatImgMarkAdd.isVisible = false

            timeHasStarted = false
            binding.floatButtonPanel.floatTvCurrent.isVisible = false
            binding.floatButtonPanel.floatTvDuration.isVisible = false
            binding.floatButtonPanel.tvSeekTime.isVisible = false
            binding.floatButtonPanel.imgCloseHandle.isVisible = false
        }
    }

    private val panelExpandEndListener = COUIDynamicAnimation.OnAnimationEndListener { animation, canceled, value, velocity ->
        binding.panelOverlay.visible()
    }

    private val panelCollapseEndListener = COUIDynamicAnimation.OnAnimationEndListener { animation, canceled, value, velocity ->
        binding.panelOverlay.gone()
    }

    /**
     * 初始化UI参数
     */
    fun initLayout() {
        //手动设置end约束，让它的marginLeft 和 marginRight 失效
        binding.floatButtonPanel.imgWaveFormBg.updateLayoutParams<ConstraintLayout.LayoutParams> {
            endToEnd = ConstraintLayout.LayoutParams.PARENT_ID
        }

        //绘制阴影
        ShadowUtils.setElevationToView(
            binding.floatButtonPanel.panel,
            ShadowUtils.SHADOW_LV3,
            context.resources.getDimensionPixelOffset(com.support.appcompat.R.dimen.support_shadow_size_level_three),
            COUIContextUtil.getColor(context, R.color.playback_suspension_panel_shadow_color)
        )
    }

    /**
     * 初始化监听
     */
    @SuppressLint("ClickableViewAccessibility")
    fun initListener() {
        binding.floatButtonPanel.panel.setOnTouchListener { v, event -> // 防止事件向下传递
            true
        }

        //点击关闭
        binding.floatButtonPanel.imgCloseHandle.setOnClickListener {
            startCollapseAnimation()
        }
    }

    /**
     * 播放面板折叠/展开
     */
    fun startExpandCollapseAnimation(isExpand: Boolean) {
        if (isExpand) {
            //展开状态就折叠
            startCollapseAnimation()
        } else {
            //折叠状态就展开
            startExpandAnimation()
        }
    }

    /**
     * 播放面板是否展开
     */
    fun isOpen(): Boolean {
        return mViewModel?.isFloatPanelState?.value ?: false
    }

    /**
     * 展开
     */
    fun startExpandAnimation() {
        mViewModel?.isFloatPanelState?.value = true

        playActionAnimation()
        panelAnimation()
        forwardAndBackAlphaAnimation()
        markListActionAnimation()
        timeTextActionAnimation()
        waveFormActionAnimation()
    }

    /**
     * 折叠
     */
    fun startCollapseAnimation() {
        if (mViewModel?.isFloatPanelState?.value == false) return

        mViewModel?.isFloatPanelState?.value = false
        isRotateAttribute()
        playActionRecoverAnimation()
        panelRecoverAnimation()
        forwardAndBackRecoverAnimation()
        markListRecoverAnimation()
        seekTimeRecoverAnimation()
        waveFormRecoverAnimation()
    }

    /**
     * 如果屏幕旋转过 那么所有的start和final值就要交换
     */
    fun isRotateAttribute() {
        if (isRotate) {
            var tempAttribute = distancePlayX
            distancePlayX = distancePlayXNormal
            distancePlayXNormal = -tempAttribute

            tempAttribute = distancePlayY
            distancePlayY = distancePlayYNormal
            distancePlayYNormal = -tempAttribute

            tempAttribute = distanceMarkListX
            distanceMarkListX = distanceMarkListXNormal
            distanceMarkListXNormal = -tempAttribute

            tempAttribute = distanceMarkListY
            distanceMarkListY = distanceMarkListYNormal
            distanceMarkListYNormal = -tempAttribute

            tempAttribute = distanceSeekTimeY
            distanceSeekTimeY = distanceSeekTimeNormalY
            distanceSeekTimeNormalY = -tempAttribute

            tempAttribute = distanceWaveFormY
            distanceWaveFormY = distanceWaveFormYNormal
            distanceWaveFormYNormal = -tempAttribute

            tempAttribute = forwardTranslationX
            forwardTranslationX = forwardTranslationXNormal
            forwardTranslationXNormal = -tempAttribute

            tempAttribute = buttonTranslationY
            buttonTranslationY = buttonTranslationYNormal
            buttonTranslationYNormal = -tempAttribute

            tempAttribute = backwardTranslationX
            backwardTranslationX = backwardTranslationXNormal
            backwardTranslationXNormal = -tempAttribute

            tempAttribute = markAddTranslationX
            markAddTranslationX = markAddTranslationXNormal
            markAddTranslationXNormal = -tempAttribute

            isRotate = false
        }
    }

    /**
     * 播放按钮大小、位置变化动画start
     */
    private fun playActionAnimation() {
        if (playScaleAnimation == null) {
            initPlayActionAnimation()
        } else {
            playScaleAnimation?.let {
                it.setStartValue(playButtonNormal)
                it.spring.finalPosition = expandPlayButtonSize
            }
            playTranslationX?.let {
                it.setStartValue(distancePlayXNormal)
                it.spring.finalPosition = distancePlayX
            }

            playTranslationY?.let {
                it.setStartValue(distancePlayYNormal)
                it.spring.finalPosition = distancePlayY
            }

            shadowTranslationX?.let {
                it.setStartValue(distancePlayXNormal)
                it.spring.finalPosition = distancePlayX
            }
            shadowTranslationY?.let {
                it.setStartValue(distancePlayYNormal)
                it.spring.finalPosition = distancePlayY
            }
            circleRadiusScaleAnimation?.let {
                it.setStartValue(playButtonIconNormal)
                it.spring.finalPosition = expandPlayButtonIconSize
            }
        }
        playScaleAnimation?.start()
        circleRadiusScaleAnimation?.start()
        playTranslationX?.start()
        playTranslationY?.start()
        shadowTranslationX?.start()
        shadowTranslationY?.start()
        shadowAlphaAnimation?.start()
    }

    /**
     * 初始化播放按钮大小、位置变化动画
     */
    fun initPlayActionAnimation() {
        initPlayScaleAnimation()
        initCircleRadiusScaleAnimation()

        //播放按钮X轴居中 父控件中点位置 - rightMargin -放大后的尺寸/2
        distancePlayX = if (BaseApplication.sIsRTLanguage) {
            distancePlayXNormal = binding.floatButtonPanel.middleControl.marginRight.toFloat()
            binding.floatButtonPanel.panel.width / 2 - playButtonNormalEndMargin - expandPlayButtonSize / 2
        } else {
            distancePlayXNormal = binding.floatButtonPanel.middleControl.marginLeft.toFloat()
            -(binding.floatButtonPanel.panel.width / 2 - playButtonNormalEndMargin - expandPlayButtonSize / 2)
        }

        playTranslationX = COUISpringAnimation(binding.floatButtonPanel.middleControl, COUIDynamicAnimation.TRANSLATION_X).apply {
            setStartValue(distancePlayXNormal)
            spring = getDefaultSpringForce(distancePlayX, DEFAULT_SPRING__RESPONSE)
        }

        shadowTranslationX = COUISpringAnimation(binding.floatButtonPanel.playButtonShadow, COUIDynamicAnimation.TRANSLATION_X).apply {
            setStartValue(distancePlayXNormal)
            spring = getDefaultSpringForce(distancePlayX, DEFAULT_SPRING__RESPONSE)
        }

        //播放按钮Y轴平移 下边距
        distancePlayY = -(playbackMarginBottom - playButtonNormalBottom)
        distancePlayYNormal = binding.floatButtonPanel.middleControl.marginTop.toFloat()
        playTranslationY = COUISpringAnimation(binding.floatButtonPanel.middleControl, COUIDynamicAnimation.TRANSLATION_Y).apply {
            setStartValue(distancePlayYNormal)
            spring = getDefaultSpringForce(distancePlayY, DEFAULT_SPRING__RESPONSE)
        }

        shadowTranslationY = COUISpringAnimation(binding.floatButtonPanel.playButtonShadow, COUIDynamicAnimation.TRANSLATION_Y).apply {
            setStartValue(distancePlayYNormal)
            spring = getDefaultSpringForce(distancePlayY, DEFAULT_SPRING__RESPONSE)
        }

        shadowAlphaAnimation =
            ObjectAnimator.ofFloat(binding.floatButtonPanel.playButtonShadow, PROPERTY_ALPHA_KEY, PROPERTY_ALPHA_TRANSPARENT, PROPERTY_ALPHA_FULL)
        shadowAlphaAnimation?.addListener {
            it.doOnStart { binding.floatButtonPanel.playButtonShadow.isVisible = true }
        }
    }

    /**
     * 初始化播放按钮大小动画
     */
    private fun initPlayScaleAnimation() {
        val playButton = binding.floatButtonPanel.middleControl
        playButton.bringToFront()
        //播放暂停按钮放大
        playScaleAnimation = COUISpringAnimation(playButton, COUIDynamicAnimation.ALPHA)
        val sizeSpring = getDefaultSpringForce(expandPlayButtonSize, DEFAULT_SPRING__RESPONSE)
        playButtonNormal = playButton.height.toFloat()
        playScaleAnimation?.let {
            it.setStartValue(playButtonNormal)
            it.spring = sizeSpring
            it.addUpdateListener { animation, value, velocity ->
                //外层的RelativeLayout
                binding.floatButtonPanel.middleControl.updateLayoutParams<ViewGroup.LayoutParams> {
                    height = value.toInt()
                    width = value.toInt()
                }

                //里面的button
                binding.floatButtonPanel.floatRedCircleIcon.updateLayoutParams<ViewGroup.LayoutParams> {
                    height = value.toInt()
                    width = value.toInt()
                }

                binding.floatButtonPanel.playButtonShadow.updateLayoutParams<ViewGroup.LayoutParams> {
                    height = value.toInt()
                    width = value.toInt()
                }
            }
        }
    }

    /**
     * 初始化圆角度数变化动画
     */
    private fun initCircleRadiusScaleAnimation() {
        val redirectIcon  = binding.floatButtonPanel.floatRedCircleIcon
        redirectIcon.bringToFront()
        //播放暂停按钮里面的Radius缩放
        circleRadiusScaleAnimation = COUISpringAnimation(redirectIcon, COUIDynamicAnimation.ALPHA)
        val iconSizeSpring = getDefaultSpringForce(expandPlayButtonIconSize, DEFAULT_SPRING__RESPONSE)
        circleRadiusScaleAnimation?.let {
            it.setStartValue(playButtonIconNormal)
            it.spring = iconSizeSpring
            it.addUpdateListener { animation, value, velocity ->
                binding.floatButtonPanel.floatRedCircleIcon.refreshCircleRadius(value)
            }
        }
    }

    /**
     * 播放按钮大小、位置还原
     */
    private fun playActionRecoverAnimation() {
        if (shadowAlphaRecoverAnimation == null) {
            shadowAlphaRecoverAnimation =
                ObjectAnimator.ofFloat(binding.floatButtonPanel.playButtonShadow, PROPERTY_ALPHA_KEY, PROPERTY_ALPHA_FULL, PROPERTY_ALPHA_TRANSPARENT)
            shadowAlphaRecoverAnimation?.addListener {
                it.doOnEnd { binding.floatButtonPanel.playButtonShadow.isVisible = false }
            }
        }
        playScaleAnimation?.let {
            it.setStartValue(expandPlayButtonSize)
            it.animateToFinalPosition(playButtonNormal)
        }
        circleRadiusScaleAnimation?.let {
            it.setStartValue(expandPlayButtonIconSize)
            it.animateToFinalPosition(playButtonIconNormal)
        }
        playTranslationX?.let {
            it.setStartValue(distancePlayX)
            it.animateToFinalPosition(distancePlayXNormal)
        }
        playTranslationY?.let {
            it.setStartValue(distancePlayY)
            it.animateToFinalPosition(distancePlayYNormal)
        }

        shadowTranslationX?.let {
            it.setStartValue(distancePlayX)
            it.animateToFinalPosition(distancePlayXNormal)
        }
        shadowTranslationY?.let {
            it.setStartValue(distancePlayY)
            it.animateToFinalPosition(distancePlayYNormal)
        }
        shadowAlphaRecoverAnimation?.start()
    }

    /**
     * 面板高度、圆角度变化动画start
     */
    private fun panelAnimation() {
        if (panelAnimator == null) {
            initPanelAnimation()
        } else {
            panelAnimator?.let {
                it.setStartValue(panelHeightNormal)
                it.spring.finalPosition = panelExpandHeight
                it.spring.setResponse(DEFAULT_SPRING__RESPONSE)
            }
            radiusAnimator?.let {
                it.setStartValue(RADIUS_NORMAL)
                it.spring?.finalPosition = panelExpandRadius
            }
        }
        binding.panelOverlay.visible()
        panelAnimator?.start()
        radiusAnimator?.start()
    }

    /**
     * 初始化播放面板高度、圆角度变化动画
     */
    fun initPanelAnimation() {
        initSizeAnimator()

        // 获取当前背景并创建可变副本
        val shapeDrawable = binding.floatButtonPanel.panel.background.mutate() as GradientDrawable

        // 创建自定义属性用于动画
        val radiusProperty = object : FloatPropertyCompat<GradientDrawable>("cornerRadius") {
            override fun getValue(drawable: GradientDrawable): Float {
                return drawable.cornerRadius
            }

            override fun setValue(drawable: GradientDrawable, value: Float) {
                drawable.cornerRadius = value
                binding.floatButtonPanel.panel.postInvalidate() // 更新视图
            }
        }

        radiusAnimator = COUISpringAnimation(shapeDrawable, radiusProperty)
        val radiusSpring = getDefaultSpringForce(RADIUS_ANIMATION, DEFAULT_SPRING__RESPONSE)
        radiusAnimator?.let {
            it.setStartValue(RADIUS_NORMAL)
            it.setSpring(radiusSpring)
        }
    }

    /**
     * 初始化底部面板高度变化动画
     */
    private fun initSizeAnimator() {
        //底部面板高度变化动画
        panelHeightNormal =  binding.floatButtonPanel.panel.floatPanelNormHeight.toFloat()
        panelAnimator = COUISpringAnimation(binding.floatButtonPanel.panel, COUIDynamicAnimation.ALPHA).apply {
            setStartValue(panelHeightNormal)
            spring = getDefaultSpringForce(panelExpandHeight, DEFAULT_SPRING__RESPONSE)
            addUpdateListener { animation, value, velocity ->
                binding.floatButtonPanel.panel.updateLayoutParams<FrameLayout.LayoutParams> {
                    height = value.toInt()
                }
            }
            removeEndListener(panelCollapseEndListener)
            addEndListener(panelExpandEndListener)
        }
    }

    /**
     * 播放面板高度、圆角度还原
     */
    private fun panelRecoverAnimation() {
        if (panelAnimator == null) {
            DebugUtil.i(TAG, "panelRecoverAnimation panelAnimator is null")
            return
        }
        panelAnimator?.let {
            it.spring.setResponse(PANEL_SPRING_RECOVER_RESPONSE)
            it.removeEndListener(panelExpandEndListener)
            it.addEndListener(panelCollapseEndListener)
            it.setStartValue(panelExpandHeight)
            it.animateToFinalPosition(panelHeightNormal)
        }

        radiusAnimator?.setStartValue(panelExpandRadius)
        radiusAnimator?.animateToFinalPosition(RADIUS_NORMAL)
    }

    /**
     * 快进、快退、标记按钮透明度动画start
     */
    private fun forwardAndBackAlphaAnimation() {
        if (forwardAlphaAnimation == null) {
            initForwardAndBackAlphaAnimation()
        } else {
            forwardTranslationXAnimation?.let {
                it.setStartValue(forwardTranslationXNormal)
                it.spring.finalPosition = forwardTranslationX
            }
            forwardTranslationYAnimation?.let {
                it.setStartValue(buttonTranslationYNormal)
                it.spring.finalPosition = buttonTranslationY
            }
            backwardTranslationXAnimation?.let {
                it.setStartValue(backwardTranslationXNormal)
                it.spring.finalPosition = backwardTranslationX
            }
            backwardTranslationYAnimation?.let {
                it.setStartValue(buttonTranslationYNormal)
                it.spring.finalPosition = buttonTranslationY
            }
            markAddTranslationXAnimation?.let {
                it.setStartValue(markAddTranslationXNormal)
                it.spring.finalPosition = markAddTranslationX
            }
            markAddTranslationYAnimation?.let {
                it.setStartValue(buttonTranslationYNormal)
                it.spring.finalPosition = buttonTranslationY
            }
            forwardAlphaAnimation?.spring?.finalPosition = 1f
            backwardAlphaAnimation?.spring?.finalPosition = 1f
            markAddAlphaAnimation?.spring?.finalPosition = 1f
        }

        forwardAndBackDelayJob = CoroutineScope(Dispatchers.Main).launch {
            delay(FORWARD_BACKWARD_DELAYED_TIME)
            //透明度动画要比其它动画延迟播放
            forwardAlphaAnimation?.start()
            backwardAlphaAnimation?.start()
            markAddAlphaAnimation?.start()
        }
        forwardTranslationXAnimation?.start()
        forwardTranslationYAnimation?.start()
        backwardTranslationXAnimation?.start()
        backwardTranslationYAnimation?.start()
        markAddTranslationXAnimation?.start()
        markAddTranslationYAnimation?.start()
    }

    /**
     * 初始化快进、快退、标记按钮透明度动画
     */
    fun initForwardAndBackAlphaAnimation() {
        forwardAlphaAnimation = COUISpringAnimation(binding.floatButtonPanel.floatImgForward, COUIDynamicAnimation.ALPHA)
        backwardAlphaAnimation = COUISpringAnimation(binding.floatButtonPanel.floatImgBackward, COUIDynamicAnimation.ALPHA)
        markAddAlphaAnimation = COUISpringAnimation(binding.floatButtonPanel.floatImgMarkAdd, COUIDynamicAnimation.ALPHA)
        val defaultSpring = getDefaultSpringForce(1f, DEFAULT_SPRING__RESPONSE)
        forwardAlphaAnimation?.let {
            it.spring = defaultSpring
            it.addUpdateListener(alphaAnimationUpdateListener)
        }
        backwardAlphaAnimation?.spring = defaultSpring
        markAddAlphaAnimation?.spring = defaultSpring

        initForwardAndBackPositionXAnimation()
    }

    /**
     * 初始化快进、快退、标记按钮X轴参数
     */
    private fun initForwardAndBackPositionXAttribute() {
        if (BaseApplication.sIsRTLanguage) {
            forwardTranslationX = buttonDistance
            forwardTranslationXNormal = binding.floatButtonPanel.floatImgForward.marginRight.toFloat()

            backwardTranslationX = -buttonDistance
            backwardTranslationXNormal = binding.floatButtonPanel.floatImgBackward.marginLeft.toFloat()

            markAddTranslationX = buttonDistance
            markAddTranslationXNormal = binding.floatButtonPanel.floatImgMarkAdd.marginRight.toFloat()
        } else {
            forwardTranslationX = -buttonDistance
            forwardTranslationXNormal = binding.floatButtonPanel.floatImgForward.marginLeft.toFloat()

            backwardTranslationX = buttonDistance
            backwardTranslationXNormal = binding.floatButtonPanel.floatImgBackward.marginRight.toFloat()

            markAddTranslationX = -buttonDistance
            markAddTranslationXNormal = binding.floatButtonPanel.floatImgMarkAdd.marginLeft.toFloat()
        }
    }

    /**
     * 初始化快进、快退、标记按钮X轴动画
     */
    private fun initForwardAndBackPositionXAnimation() {
        initForwardAndBackPositionXAttribute()
        markAddTranslationXAnimation = COUISpringAnimation(binding.floatButtonPanel.floatImgMarkAdd, COUIDynamicAnimation.TRANSLATION_X).apply {
            setStartValue(markAddTranslationXNormal)
            spring = getDefaultSpringForce(markAddTranslationX, DEFAULT_SPRING__RESPONSE)
        }
        forwardTranslationXAnimation = COUISpringAnimation(binding.floatButtonPanel.floatImgForward, COUIDynamicAnimation.TRANSLATION_X).apply {
            setStartValue(forwardTranslationXNormal)
            spring = getDefaultSpringForce(forwardTranslationX, DEFAULT_SPRING__RESPONSE)
        }
        backwardTranslationXAnimation = COUISpringAnimation(binding.floatButtonPanel.floatImgBackward, COUIDynamicAnimation.TRANSLATION_X).apply {
            setStartValue(backwardTranslationXNormal)
            spring = getDefaultSpringForce(backwardTranslationX, DEFAULT_SPRING__RESPONSE)
        }

        initForwardAndBackPositionYAnimation()
    }

    /**
     * 初始化快进、快退、标记按钮Y轴动画
     */
    private fun initForwardAndBackPositionYAnimation() {
        buttonTranslationYNormal = binding.floatButtonPanel.floatImgForward.marginTop.toFloat()
        buttonTranslationY = -buttonDistance

        forwardTranslationYAnimation = COUISpringAnimation(binding.floatButtonPanel.floatImgForward, COUIDynamicAnimation.TRANSLATION_Y).apply {
            setStartValue(buttonTranslationYNormal)
            spring = getDefaultSpringForce(buttonTranslationY, DEFAULT_SPRING__RESPONSE)
        }

        backwardTranslationYAnimation = COUISpringAnimation(binding.floatButtonPanel.floatImgBackward, COUIDynamicAnimation.TRANSLATION_Y).apply {
            setStartValue(buttonTranslationYNormal)
            spring = getDefaultSpringForce(buttonTranslationY, DEFAULT_SPRING__RESPONSE)
        }

        markAddTranslationYAnimation = COUISpringAnimation(binding.floatButtonPanel.floatImgMarkAdd, COUIDynamicAnimation.TRANSLATION_Y).apply {
            setStartValue(buttonTranslationYNormal)
            spring = getDefaultSpringForce(buttonTranslationY, DEFAULT_SPRING__RESPONSE)
        }
    }

    /**
     * 快进、快退、标记按钮透明度还原
     */
    private fun forwardAndBackRecoverAnimation() {
        if (forwardRecoverAlphaAnimation == null) {
            initForwardAndBackRecoverAnimation()
        }

        cancelAlphaAnimation()

        forwardTranslationXAnimation?.let {
            it.setStartValue(forwardTranslationX)
            it.animateToFinalPosition(forwardTranslationXNormal)
        }
        forwardTranslationYAnimation?.let {
            it.setStartValue(buttonTranslationY)
            it.animateToFinalPosition(buttonTranslationYNormal)
        }
        backwardTranslationXAnimation?.let {
            it.setStartValue(backwardTranslationX)
            it.animateToFinalPosition(backwardTranslationXNormal)
        }
        backwardTranslationYAnimation?.let {
            it.setStartValue(buttonTranslationY)
            it.animateToFinalPosition(buttonTranslationYNormal)
        }
        markAddTranslationXAnimation?.let {
            it.setStartValue(markAddTranslationX)
            it.spring.finalPosition = markAddTranslationXNormal
        }

        animatorSet?.start()
    }

    /**
     * 防止Spring动画还未播放完毕就开始播放透明度还原动画，导致动画播放混乱
     * */
    private fun cancelAlphaAnimation() {
        //取消正在播放中的Spring 动画
        markAddTranslationXAnimation?.cancel()
        markAddTranslationYAnimation?.cancel()
        forwardTranslationXAnimation?.cancel()
        forwardTranslationYAnimation?.cancel()
        forwardAlphaAnimation?.cancel()
        backwardTranslationXAnimation?.cancel()
        backwardTranslationYAnimation?.cancel()
        backwardAlphaAnimation?.cancel()
        markAddAlphaAnimation?.cancel()
        currentTimeAlphaAnimation?.cancel()
        durationTimeAlphaAnimation?.cancel()
        seekTimeAlphaAnimation?.cancel()
        closeHandleAlphaAnimation?.cancel()

        //取消延迟执行的Spring 动画
        forwardAndBackDelayJob?.cancel()
        forwardAndBackDelayJob = null
        timeTextActionDelayJob?.cancel()
        timeTextActionDelayJob = null
    }

    /**
     * 初始化快进、快退、标记按钮、当前时间、总时间透明度还原动画
     */
    private fun initForwardAndBackRecoverAnimation() {
        //快进、快退、标记按钮
        forwardRecoverAlphaAnimation = ObjectAnimator.ofFloat(binding.floatButtonPanel.floatImgForward, "alpha", 1f, 0f).apply {
            addListener(reverseAlphaAnimationListener)
        }
        backwardRecoverAlphaAnimation = ObjectAnimator.ofFloat(binding.floatButtonPanel.floatImgBackward, "alpha", 1f, 0f)
        markAddRecoverAlphaAnimation = ObjectAnimator.ofFloat(binding.floatButtonPanel.floatImgMarkAdd, "alpha", 1f, 0f)

        //当前时间、总时间、seek时间、把手
        currentTimeRecoverAlphaAnimation =  ObjectAnimator.ofFloat(binding.floatButtonPanel.floatTvCurrent, "alpha", 1f, 0f)
        durationTimeRecoverAlphaAnimation = ObjectAnimator.ofFloat(binding.floatButtonPanel.floatTvDuration, "alpha", 1f, 0f)
        seekTimeRecoverAlphaAnimation = ObjectAnimator.ofFloat(binding.floatButtonPanel.tvSeekTime, "alpha", 1f, 0f)
        closeHandRecoverAlphaAnimation = ObjectAnimator.ofFloat(binding.floatButtonPanel.imgCloseHandle, "alpha", 1f, 0f)

        animatorSet = AnimatorSet().apply {
            duration = FORWARD_BACKWARD_REVERSE_TIME
            interpolator = COUIMoveEaseInterpolator()
            // 同时播放
            playTogether(
                forwardRecoverAlphaAnimation,
                backwardRecoverAlphaAnimation,
                markAddRecoverAlphaAnimation,
                currentTimeRecoverAlphaAnimation,
                durationTimeRecoverAlphaAnimation,
                seekTimeRecoverAlphaAnimation,
                closeHandRecoverAlphaAnimation)
        }
    }

    /**
     * 标记列表动画start
     */
    private fun markListActionAnimation() {
        if (markListTranslationX == null) {
            initMarkListActionAnimation()
        } else {
            markListTranslationX?.let {
                it.setStartValue(distanceMarkListXNormal)
                it.spring.finalPosition = distanceMarkListX
            }
            markListTranslationY?.let {
                it.setStartValue(distanceMarkListYNormal)
                it.spring.finalPosition = distanceMarkListY
            }
            markListScaleAnimation?.setStartValue(markListSizeNormal)
            markListScaleAnimation?.spring?.finalPosition = markListSize
        }
        markListTranslationX?.start()
        markListTranslationY?.start()
        markListScaleAnimation?.start()
        markListRecoverAlphaAnimation?.cancel()
        markListAlphaAnimation?.start()
    }

    /**
     * 初始化标记列表动画
     */
    fun initMarkListActionAnimation() {
        initPosition()
        initMarkListSizeAnimation()

        //标记按钮背景透明
        markListAlphaAnimation = ObjectAnimator.ofFloat(binding.floatButtonPanel.markListBg, "alpha", 1f, 0f)
        markListAlphaAnimation?.let {
            it.duration = MARK_LIST_ACTION_TIME
            it.interpolator = COUIMoveEaseInterpolator()
        }
    }

    /**
     * 初始化标记列表XY轴动画
     */
    private fun initPosition() {
        //标记按钮X轴平移 左边距
        distanceMarkListX = if (BaseApplication.sIsRTLanguage) {
            distanceMarkListXNormal = binding.floatButtonPanel.floatMarkList.marginLeft.toFloat()
            -(markListMarginLeft - binding.floatButtonPanel.floatMarkList.marginRight)
        } else {
            distanceMarkListXNormal = binding.floatButtonPanel.floatMarkList.marginRight.toFloat()
            markListMarginLeft - binding.floatButtonPanel.floatMarkList.marginLeft
        }

        distanceMarkListXNormal
        markListTranslationX = COUISpringAnimation(binding.floatButtonPanel.floatMarkList, COUIDynamicAnimation.TRANSLATION_X).apply {
            setStartValue(distanceMarkListXNormal)
            spring = getDefaultSpringForce(distanceMarkListX, RESPONSE_MARK_LIST_ACTION_ANIMATION)
        }

        //标记按钮Y轴平移 下边距
        distanceMarkListYNormal = binding.floatButtonPanel.middleControl.marginTop.toFloat()
        distanceMarkListY = -abs(markListMarginBottom - binding.floatButtonPanel.floatMarkList.marginBottom  - (markListParentSize-markListSize)/2)
        markListTranslationY = COUISpringAnimation(binding.floatButtonPanel.floatMarkList, COUIDynamicAnimation.TRANSLATION_Y).apply {
            setStartValue(distanceMarkListYNormal)
            spring = getDefaultSpringForce(distanceMarkListY, RESPONSE_MARK_LIST_ACTION_ANIMATION)
        }
    }

    /**
     * 初始化标记列表宽高动画
     */
    private fun initMarkListSizeAnimation() {
        //标记按钮宽高更新
        markListScaleAnimation = COUISpringAnimation(binding.floatButtonPanel.imgMarkList, COUIDynamicAnimation.ALPHA).apply {
            setStartValue(markListSizeNormal)
            spring = getDefaultSpringForce(markListSize, MARKLIST_SIZE_SPRING_RESPONSE)
            addUpdateListener { animation, value, velocity ->
                val params = binding.floatButtonPanel.imgMarkList.layoutParams
                params.height = value.toInt()
                params.width = value.toInt()
                binding.floatButtonPanel.imgMarkList.layoutParams = params
            }
        }
    }

    /**
     * 标记列表动画还原
     */
    private fun markListRecoverAnimation() {
        markListTranslationX?.let {
            it.spring.setResponse(DEFAULT_SPRING__RESPONSE)
            it.setStartValue(distanceMarkListX)
            it.animateToFinalPosition(distanceMarkListXNormal)
        }

        markListTranslationY?.let {
            it.spring.setResponse(DEFAULT_SPRING__RESPONSE)
            it.setStartValue(distanceMarkListY)
            it.animateToFinalPosition(distanceMarkListYNormal)
        }

        markListScaleAnimation?.setStartValue(markListSize)
        markListScaleAnimation?.animateToFinalPosition(markListSizeNormal)

        if (markListRecoverAlphaAnimation == null) {
            markListRecoverAlphaAnimation = COUISpringAnimation(binding.floatButtonPanel.markListBg, COUIDynamicAnimation.ALPHA)
            val spring = getDefaultSpringForce(1f, DEFAULT_SPRING__RESPONSE)
            markListRecoverAlphaAnimation?.spring = spring
        } else {
            markListRecoverAlphaAnimation?.spring?.finalPosition = 1f
        }

        markListRecoverAlphaAnimation?.start()
    }

    /**
     *  当前播放时间、总时间、seek时间、关闭把手动画start
     */
    private fun timeTextActionAnimation() {
        if (currentTimeAlphaAnimation == null) {
            initTimeTextActionAnimation()
        } else {
            currentTimeAlphaAnimation?.spring?.finalPosition = 1f
            durationTimeAlphaAnimation?.spring?.finalPosition = 1f
            seekTimeAlphaAnimation?.spring?.finalPosition = 1f
            seekTimeTranslationY?.let {
                it.setStartValue(distanceSeekTimeNormalY)
                it.spring.finalPosition = distanceSeekTimeY
            }

            closeHandleAlphaAnimation?.spring?.finalPosition = 1f
        }

        timeTextActionDelayJob = CoroutineScope(Dispatchers.Main).launch {
            delay(TIME_DELAYED_TIME)
            currentTimeAlphaAnimation?.start()
            durationTimeAlphaAnimation?.start()
            seekTimeAlphaAnimation?.start()
            seekTimeTranslationY?.start()
            closeHandleAlphaAnimation?.start()
        }
    }

    /**
     * 初始化当前播放时间、总时间动画、seek时间、关闭把手
     */
    fun initTimeTextActionAnimation() {
        currentTimeAlphaAnimation = COUISpringAnimation(binding.floatButtonPanel.floatTvCurrent, COUIDynamicAnimation.ALPHA)
        durationTimeAlphaAnimation = COUISpringAnimation(binding.floatButtonPanel.floatTvDuration, COUIDynamicAnimation.ALPHA)
        seekTimeAlphaAnimation = COUISpringAnimation(binding.floatButtonPanel.tvSeekTime, COUIDynamicAnimation.ALPHA)
        closeHandleAlphaAnimation = COUISpringAnimation(binding.floatButtonPanel.imgCloseHandle, COUIDynamicAnimation.ALPHA)

        distanceSeekTimeNormalY = binding.floatButtonPanel.tvSeekTime.marginTop.toFloat()
        distanceSeekTimeY = -seekTimeY
        seekTimeTranslationY = COUISpringAnimation(binding.floatButtonPanel.tvSeekTime, COUIDynamicAnimation.TRANSLATION_Y).apply {
            setStartValue(distanceSeekTimeNormalY)
            spring = getDefaultSpringForce(distanceSeekTimeY, SEEK_TIME_SPRING__RESPONSE)
        }

        val spring = getDefaultSpringForce(1f, TIME_SPRING__RESPONSE)
        currentTimeAlphaAnimation?.spring = spring
        durationTimeAlphaAnimation?.spring = spring
        seekTimeAlphaAnimation?.spring = spring
        closeHandleAlphaAnimation?.spring = spring

        currentTimeAlphaAnimation?.addUpdateListener(timeAlphaAnimationUpdateListener)
    }

    /**
     * seek动画还原
     */
    private fun seekTimeRecoverAnimation() {
        seekTimeTranslationY?.let {
            it.setStartValue(distanceSeekTimeY)
            it.animateToFinalPosition(distanceSeekTimeNormalY)
        }
    }

    /**
     * 波形图背景动画start
     */
    private fun waveFormActionAnimation() {
        if (waveFormBgTranslationY == null) {
            initWaveFormActionAnimation()
        } else {
            waveFormBgTranslationY?.let {
                it.setStartValue(distanceWaveFormYNormal)
                it.spring.finalPosition = distanceWaveFormY
            }
            waveFormBgHeightAnimation?.let {
                it.setStartValue(waveFormBgHeightNormal)
                it.spring.finalPosition = waveFormExpandHeight
            }
            waveFormBgWidthAnimation?.let {
                it.setStartValue(waveFormBgWidthNormal)
                it.spring.finalPosition = binding.floatButtonPanel.panel.width.toFloat()
            }
            waveFormBgRadiusAnimator?.let {
                it.setStartValue(WAVEFORM_RADIUS_NORMAL)
                it.spring?.finalPosition = 0f
                it.removeEndListener(waveFormBgRecoverRadiusEndListener)
                it.addEndListener(waveFormBgRadiusEndListener)
            }
        }

        waveFormBgDelayJob = CoroutineScope(Dispatchers.Main).launch {
            delay(WAVE_FORM_BG_DELAYED_TIME)
            waveFormBgTranslationY?.start()
            waveFormBgHeightAnimation?.start()
            waveFormBgWidthAnimation?.start()
            waveFormBgRadiusAnimator?.start()
        }
    }

    /**
     * 初始化波形图背景动画
     */
    fun initWaveFormActionAnimation() {
        initWaveFormPositonAnimation()
        initWaveFormSizeAnimation()
        initWaveRadiusAnimation()
    }

    /**
     * 初始化波形图背景Y轴动画
     */
    private fun initWaveFormPositonAnimation() {
        //化波形图Y轴平移
        distanceWaveFormYNormal = binding.floatButtonPanel.imgWaveFormBg.marginTop.toFloat()
        distanceWaveFormY = -abs(waveFormMarginBottom - binding.floatButtonPanel.imgWaveFormBg.marginBottom)
        waveFormBgTranslationY = COUISpringAnimation(binding.floatButtonPanel.imgWaveFormBg, COUIDynamicAnimation.TRANSLATION_Y).apply {
            setStartValue(distanceWaveFormYNormal)
            spring = getDefaultSpringForce(distanceWaveFormY, WAVE_FORM_BG_SPRING__RESPONSE)
        }
    }

    /**
     * 初始化波形图背景宽高度动画
     */
    private fun initWaveFormSizeAnimation() {
        //高度变化
        waveFormBgHeightAnimation = COUISpringAnimation(binding.floatButtonPanel.imgWaveFormBg, COUIDynamicAnimation.ALPHA)
        val springHeight = getDefaultSpringForce(waveFormExpandHeight, WAVE_FORM_BG_SPRING__RESPONSE)
        waveFormBgHeightNormal = binding.floatButtonPanel.imgWaveFormBg.height.toFloat()
        waveFormBgHeightAnimation?.let {
            it.setStartValue(waveFormBgHeightNormal)
            it.spring = springHeight
            it.addUpdateListener { animation, value, velocity ->
                binding.floatButtonPanel.imgWaveFormBg.updateLayoutParams<ViewGroup.LayoutParams> {
                    height =  value.toInt()
                }
            }
        }

        //宽度变化
        waveFormBgWidthAnimation = COUISpringAnimation(binding.floatButtonPanel.imgWaveFormBg, COUIDynamicAnimation.ALPHA)
        val springWidth = getDefaultSpringForce(binding.floatButtonPanel.panel.width.toFloat(), WAVE_FORM_BG_SPRING__RESPONSE)
        waveFormBgWidthNormal = binding.floatButtonPanel.imgWaveFormBg.width.toFloat()
        waveFormBgWidthAnimation?.let {
            it.setStartValue(waveFormBgWidthNormal)
            it.spring = springWidth
            it.addUpdateListener { animation, value, velocity ->
                binding.floatButtonPanel.imgWaveFormBg.updateLayoutParams<ViewGroup.LayoutParams> {
                    width =  value.toInt()
                }
            }
        }
    }

    /**
     * 初始化波形图背景圆角变化度动画
     */
    private fun initWaveRadiusAnimation() {
        //圆角
        val shapeDrawable = binding.floatButtonPanel.imgWaveFormBg.background.mutate() as GradientDrawable

        // 创建自定义属性用于动画
        val radiusProperty = object : FloatPropertyCompat<GradientDrawable>("cornerRadius") {
            override fun getValue(drawable: GradientDrawable): Float {
                return drawable.cornerRadius
            }

            override fun setValue(drawable: GradientDrawable, value: Float) {
                drawable.cornerRadius = value
                binding.floatButtonPanel.imgWaveFormBg.postInvalidate() // 更新视图
            }
        }

        waveFormBgRadiusAnimator = COUISpringAnimation(shapeDrawable, radiusProperty)
        val radiusSpring = getDefaultSpringForce(0f, WAVE_FORM_BG_RADIUS_SPRING__RESPONSE)
        waveFormBgRadiusAnimator?.let {
            it.setStartValue(WAVEFORM_RADIUS_NORMAL)
            it.spring = radiusSpring
            it.addEndListener(waveFormBgRadiusEndListener)
        }
    }


    /**
     * 波形图背景动画动画还原
     */
    private fun waveFormRecoverAnimation() {
        //先把正在播放中的动画和延时任务取消
        waveFormBgDelayJob?.cancel()
        waveFormBgDelayJob = null

        //再执行还原动画
        waveFormBgTranslationY?.let {
            it.cancel()
            it.setStartValue(distanceWaveFormY)
            it.animateToFinalPosition(distanceWaveFormYNormal)
        }

        waveFormBgHeightAnimation?.let {
            it.cancel()
            it.setStartValue(waveFormExpandHeight)
            it.animateToFinalPosition(waveFormBgHeightNormal)
        }

        waveFormBgWidthAnimation?.let {
            it.cancel()
            it.setStartValue(binding.floatButtonPanel.panel.width.toFloat())
            it.animateToFinalPosition(waveFormBgWidthNormal)
        }

        waveFormBgRadiusAnimator?.let {
            it.cancel()
            it.removeEndListener(waveFormBgRadiusEndListener)
            it.addEndListener(waveFormBgRecoverRadiusEndListener)
            it.setStartValue(0f)
            it.animateToFinalPosition(WAVEFORM_RADIUS_NORMAL)
        }
    }

    private fun getDefaultSpringForce(finalPosition: Float, response: Float): COUISpringForce =
        COUISpringForce().setBounce(DEFAULT_SPRING_BOUNCE)
            .setFinalPosition(finalPosition)
            .setResponse(response)

    private fun cancelAnimation() {
        waveFormBgRadiusAnimator?.let {
            it.removeEndListener(waveFormBgRadiusEndListener)
            it.removeEndListener(waveFormBgRecoverRadiusEndListener)
        }
        forwardAlphaAnimation?.removeUpdateListener(alphaAnimationUpdateListener)
        forwardRecoverAlphaAnimation?.removeListener(reverseAlphaAnimationListener)
        currentTimeAlphaAnimation?.removeUpdateListener(timeAlphaAnimationUpdateListener)
        shadowAlphaAnimation?.removeAllListeners()
        shadowAlphaRecoverAnimation?.removeAllListeners()
        animatorSet?.cancel()

        cancelAnimation(playScaleAnimation)
        cancelAnimation(circleRadiusScaleAnimation)
        cancelAnimation(playTranslationX)
        cancelAnimation(playTranslationY)
        cancelAnimation(shadowTranslationX)
        cancelAnimation(shadowTranslationY)
        cancelAnimation(panelAnimator)
        cancelAnimation(radiusAnimator)

        cancelAnimation(forwardTranslationXAnimation)
        cancelAnimation(forwardTranslationYAnimation)
        cancelAnimation(forwardAlphaAnimation)
        cancelAnimation(backwardAlphaAnimation)
        cancelAnimation(backwardTranslationXAnimation)
        cancelAnimation(backwardTranslationYAnimation)
        cancelAnimation(markAddAlphaAnimation)
        cancelAnimation(markAddTranslationXAnimation)
        cancelAnimation(markAddTranslationYAnimation)

        cancelAnimation(markListTranslationX)
        cancelAnimation(markListTranslationY)
        cancelAnimation(markListScaleAnimation)
        markListAlphaAnimation?.cancel()
        markListRecoverAlphaAnimation?.cancel()

        cancelAnimation(currentTimeAlphaAnimation)
        cancelAnimation(durationTimeAlphaAnimation)
        cancelAnimation(seekTimeAlphaAnimation)
        cancelAnimation(seekTimeTranslationY)
        cancelAnimation(closeHandleAlphaAnimation)

        cancelAnimation(waveFormBgTranslationY)
        cancelAnimation(waveFormBgHeightAnimation)
        cancelAnimation(waveFormBgWidthAnimation)
        cancelAnimation(waveFormBgRadiusAnimator)
    }

    private fun resetAnimator() {
        cancelAnimation()
        playScaleAnimation = null
        playTranslationX = null
        playTranslationY = null
        shadowTranslationX = null
        shadowTranslationY = null
        shadowAlphaAnimation = null
        shadowAlphaRecoverAnimation = null
        panelAnimator = null
        radiusAnimator = null
        forwardAlphaAnimation = null
        forwardTranslationXAnimation = null
        forwardTranslationYAnimation = null
        backwardTranslationXAnimation = null
        backwardTranslationYAnimation = null
        backwardAlphaAnimation = null
        markAddAlphaAnimation = null
        markAddTranslationXAnimation = null
        markAddTranslationYAnimation = null
        forwardRecoverAlphaAnimation = null
        backwardRecoverAlphaAnimation = null
        markAddRecoverAlphaAnimation = null
        animatorSet = null
        markListTranslationX = null
        markListTranslationY = null
        markListScaleAnimation = null
        markListAlphaAnimation = null
        markListRecoverAlphaAnimation = null
        circleRadiusScaleAnimation = null
        currentTimeAlphaAnimation = null
        seekTimeAlphaAnimation = null
        seekTimeTranslationY = null
        closeHandleAlphaAnimation = null
        durationTimeAlphaAnimation = null
        currentTimeRecoverAlphaAnimation = null
        durationTimeRecoverAlphaAnimation = null
        seekTimeRecoverAlphaAnimation = null
        closeHandRecoverAlphaAnimation = null
        waveFormBgTranslationY = null
        waveFormBgHeightAnimation = null
        waveFormBgWidthAnimation = null
        waveFormBgRadiusAnimator = null

        forwardAndBackDelayJob?.cancel()
        forwardAndBackDelayJob = null
        timeTextActionDelayJob?.cancel()
        timeTextActionDelayJob = null
    }

    private fun cancelAnimation(animation: COUISpringAnimation?) {
        animation?.cancel()
    }

    fun isExpand(): Boolean {
        val isExpandHeight = binding.floatButtonPanel.panel.height == panelExpandHeight.toInt()
        val isExpandAnimationRunning = panelAnimator?.isRunning == true
        return (isExpandHeight || isExpandAnimationRunning) && isOpen()
    }

    fun release() {
        resetAnimator()
    }
}