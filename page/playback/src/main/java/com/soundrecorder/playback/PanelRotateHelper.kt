/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: PanelRotateHelper
 Description: 页面重构时播放面板元素直接设置到展开状态
 Version: 1.0
 Date: 2025/07/11
 Author: W9078961
 -----------Revision History-----------
 <author> <date> <version> <desc>
 */

package com.soundrecorder.playback

import android.content.Context
import android.graphics.drawable.GradientDrawable
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import com.soundrecorder.playback.ExpandCollapseAnimationHelper.Companion.RADIUS_ANIMATION
import com.soundrecorder.playback.databinding.FragmentPlaybackContainerBinding

class PanelRotateHelper(
    private val context: Context,
    private val binding: FragmentPlaybackContainerBinding,
    private val floatPanelAnimationHelper: ExpandCollapseAnimationHelper
) {

    /**
     * 页面重构时，直接将页面调整为打开状态
     */
    fun openPanel() {
        floatPanelAnimationHelper.isRotate = true
        openPanelHeight()
        openPlayAction()
        openForwardAndBackAlpha()
        openMarkList()
        openTimeTextActionAnimation()
        openWaveForm()
    }

    /**
     * 页面重构时，调整面板高度
     */
    fun openPanelHeight() {
        floatPanelAnimationHelper.initPanelAnimation()
        binding.floatButtonPanel.panel.updateLayoutParams<ViewGroup.LayoutParams> {
            height = floatPanelAnimationHelper.panelExpandHeight.toInt()
        }

        // 获取当前背景并创建可变副本
        val shapeDrawable = binding.floatButtonPanel.panel.background.mutate() as GradientDrawable
        shapeDrawable.cornerRadius = RADIUS_ANIMATION
        binding.floatButtonPanel.panel.postInvalidate() // 更新视图
    }

    /**
     * 页面重构时，调整播放按钮状态
     */
    fun openPlayAction() {
        floatPanelAnimationHelper.initPlayActionAnimation()
        openPlaySize()

        //播放按钮X轴居中 父控件中点位置 - rightMargin -放大后的尺寸/2
        val leftMarg = binding.floatButtonPanel.panel.width / 2 - floatPanelAnimationHelper.expandPlayButtonSize / 2
        binding.floatButtonPanel.middleControl.updateLayoutParams<ConstraintLayout.LayoutParams> {
            marginEnd = leftMarg.toInt()
            bottomMargin = floatPanelAnimationHelper.playbackMarginBottom.toInt()
        }

        binding.floatButtonPanel.playButtonShadow.alpha = 1f
        binding.floatButtonPanel.playButtonShadow.isVisible = true
    }

    /**
     * 页面重构时，调整播放按钮宽高
     */
    private fun openPlaySize() {
        //外层的RelativeLayout
        binding.floatButtonPanel.middleControl.updateLayoutParams<ViewGroup.LayoutParams> {
            height = floatPanelAnimationHelper.expandPlayButtonSize.toInt()
            width = floatPanelAnimationHelper.expandPlayButtonSize.toInt()
        }

        //里面的button
        binding.floatButtonPanel.floatRedCircleIcon.updateLayoutParams<ViewGroup.LayoutParams> {
            height = floatPanelAnimationHelper.expandPlayButtonSize.toInt()
            width = floatPanelAnimationHelper.expandPlayButtonSize.toInt()
        }

        binding.floatButtonPanel.playButtonShadow.updateLayoutParams<ViewGroup.LayoutParams> {
            height = floatPanelAnimationHelper.expandPlayButtonSize.toInt()
            width = floatPanelAnimationHelper.expandPlayButtonSize.toInt()
        }
        binding.floatButtonPanel.floatRedCircleIcon.refreshCircleRadius(floatPanelAnimationHelper.expandPlayButtonIconSize)
    }

    /**
     * 页面重构时，直接显示快退、快进、标记、当前时间、总时间、seek时间和把手
     */
    private fun openForwardAndBackAlpha() {
        floatPanelAnimationHelper.initForwardAndBackAlphaAnimation()
        binding.floatButtonPanel.floatImgForward.isVisible = true
        binding.floatButtonPanel.floatImgForward.alpha = 1f
        binding.floatButtonPanel.floatImgBackward.isVisible = true
        binding.floatButtonPanel.floatImgBackward.alpha = 1f
        binding.floatButtonPanel.floatImgMarkAdd.isVisible = true
        binding.floatButtonPanel.floatImgMarkAdd.alpha = 1f

        binding.floatButtonPanel.floatImgForward.updateLayoutParams<ConstraintLayout.LayoutParams> {
            marginEnd += floatPanelAnimationHelper.buttonDistance.toInt()
            bottomMargin += floatPanelAnimationHelper.buttonDistance.toInt()
        }
        binding.floatButtonPanel.floatImgBackward.updateLayoutParams<ConstraintLayout.LayoutParams> {
            marginStart += floatPanelAnimationHelper.buttonDistance.toInt()
            bottomMargin += floatPanelAnimationHelper.buttonDistance.toInt()
        }
        binding.floatButtonPanel.floatImgMarkAdd.updateLayoutParams<ConstraintLayout.LayoutParams> {
            marginEnd += floatPanelAnimationHelper.buttonDistance.toInt()
            bottomMargin += floatPanelAnimationHelper.buttonDistance.toInt()
        }
    }

    /**
     * 页面重构时，显示标记列表
     */
    private fun openMarkList() {
        floatPanelAnimationHelper.initMarkListActionAnimation()

        binding.floatButtonPanel.floatMarkList.updateLayoutParams<ConstraintLayout.LayoutParams> {
            marginStart = floatPanelAnimationHelper.markListMarginLeft.toInt()
            bottomMargin = (floatPanelAnimationHelper.markListMarginBottom -
                    (floatPanelAnimationHelper.markListParentSize - floatPanelAnimationHelper.markListSize) / 2).toInt()
        }
        binding.floatButtonPanel.imgMarkList.updateLayoutParams<ConstraintLayout.LayoutParams> {
            width = floatPanelAnimationHelper.markListSize.toInt()
            height = floatPanelAnimationHelper.markListSize.toInt()
        }

        binding.floatButtonPanel.markListBg.alpha = 0f
    }

    /**
     * 页面重构时，当前播放时间、总时间、seek时间、把手显示
     */
    private fun openTimeTextActionAnimation() {
        floatPanelAnimationHelper.initTimeTextActionAnimation()

        //当前时间、总时间、seek时间、把手
        binding.floatButtonPanel.floatTvCurrent.isVisible = true
        binding.floatButtonPanel.floatTvCurrent.alpha = 1f
        binding.floatButtonPanel.floatTvDuration.isVisible = true
        binding.floatButtonPanel.floatTvDuration.alpha = 1f
        binding.floatButtonPanel.tvSeekTime.isVisible = true
        binding.floatButtonPanel.tvSeekTime.alpha = 1f
        binding.floatButtonPanel.imgCloseHandle.isVisible = true
        binding.floatButtonPanel.imgCloseHandle.alpha = 1f

        binding.floatButtonPanel.tvSeekTime.updateLayoutParams<ConstraintLayout.LayoutParams> {
            bottomMargin += floatPanelAnimationHelper.seekTimeY.toInt()
        }
    }

    /**
     * 页面重构时，波形图位移
     */
    private fun openWaveForm() {
        floatPanelAnimationHelper.initWaveFormActionAnimation()

        binding.floatButtonPanel.imgWaveFormBg.updateLayoutParams<ConstraintLayout.LayoutParams> {
            bottomMargin = floatPanelAnimationHelper.waveFormMarginBottom.toInt()
            height = floatPanelAnimationHelper.waveFormExpandHeight.toInt()
            width = binding.floatButtonPanel.panel.width
        }

        //圆角
        val shapeDrawable = binding.floatButtonPanel.imgWaveFormBg.background.mutate() as GradientDrawable
        shapeDrawable.cornerRadius = 0f
        shapeDrawable.setColor(ContextCompat.getColor(context, R.color.wave_form_expand_bg_color))
        binding.floatButtonPanel.imgWaveFormBg.postInvalidate() // 更新视图
    }
}