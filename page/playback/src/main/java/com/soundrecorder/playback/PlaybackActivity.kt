/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: PlaybackActivity
 * Description:
 * Version: 1.0
 * Date: 2022/11/4
 * Author: ********(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * ******** 2022/11/4 1.0 create
 */
package com.soundrecorder.playback

import android.content.Intent
import android.os.Bundle
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.findFragment
import com.soundrecorder.base.ext.postValueSafe
import com.soundrecorder.base.ext.replaceFragmentByTag
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.NumberConstant
import com.soundrecorder.common.constant.Constants
import com.soundrecorder.common.databean.StartPlayModel
import com.soundrecorder.common.db.RecorderDBUtil
import com.soundrecorder.common.permission.PermissionActivity
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.modulerouter.BrowseFileInterface
import com.soundrecorder.modulerouter.playback.PlayBackInterface
import com.soundrecorder.modulerouter.utils.Injector
import kotlin.let

class PlaybackActivity : PermissionActivity() {
    companion object {
        /*用于识别,统计页面 勿改*/
        private const val FUNCTION_NAME = "Playback"
        private const val TAG = "PlaybackActivity"
        private const val TAG_FRAGMENT = "PlaybackContainerFragment"
    }

    private var mFragment: Fragment? = null

    private var mPermissionGrantedListener = {
        DebugUtil.i(TAG, "onPermissionGranted")
        if (PermissionUtils.hasReadAudioPermission()) {
            getContainerFragment()?.onPermissionGranted()
        }
    }

    private val browseFileApi by lazy {
        Injector.injectFactory<BrowseFileInterface>()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        DebugUtil.i(TAG, "onCreate")
        setPermissionGrantedListener(mPermissionGrantedListener)
        setContentView(R.layout.activity_playback_place)
        initParamsFromOutSide(savedInstanceState)
    }

    override fun onResume() {
        super.onResume()
        // 设置音频fragment的底部按钮可见
        mFragment?.let {
            val viewModel = ViewModelProvider(it)[PlaybackContainerViewModel::class.java]
            viewModel.mShowActivityControlView.postValueSafe(false)
        }
    }
    /**
     * 初始化外部传入的参数
     */
    private fun initParamsFromOutSide(savedInstanceState: Bundle?) {
        val intent = this.intent ?: return
        val mediaId = intent.getLongExtra("recordId", 0L)
        var model: StartPlayModel? = null
        if (mediaId > 0) {
            val playPath = intent.getStringExtra("playPath")
            val selectPos = intent.getStringExtra("selectPos") ?: PlayBackInterface.TAB_TYPE_CONVERT
            val autoPlay = intent.getBooleanExtra("autoPlay", true)
            val duration = intent.getLongExtra("duration", 0)

            model = StartPlayModel(
                mediaId = mediaId,
                playPath = playPath,
                selectPosInPlayback = selectPos,
                autoPlay = autoPlay,
                duration = duration
            )
            DebugUtil.d(TAG, "initParamsFromOutSide mediaId=$mediaId playPath=$playPath")
        }
        if (savedInstanceState == null) {
            mFragment = PlaybackContainerFragment().apply {
                val showLoading = checkDecodeAmpOverLimit(model)
                arguments = bundleOf(PlaybackContainerFragment.ARG_KEY_SHOW_LOADING to showLoading)
            }
            replaceFragmentByTag(R.id.fl_activity_playback, mFragment, TAG_FRAGMENT)
        }

        val browseFileActivityViewModel = browseFileApi?.getBrowseActivityViewModel(this)
        browseFileApi?.setViewModelPlayData(browseFileActivityViewModel, model)
        browseFileApi?.setViewModelWindowType(browseFileActivityViewModel, resources.configuration)
    }

    /**
     * 预估加载波形时间是否超过 400ms
     * 1. 波形已在本地，判断为 400ms内,由于设备性能不一，若超过2小时，判断为 400ms+
     * 2.波形未解析，超过1分钟，则认为需要耗时 400ms+
     */
    private fun checkDecodeAmpOverLimit(playModel: StartPlayModel?): Boolean {
        if (playModel == null) {
            return false
        }
        if (playModel.duration > Constants.TIME_ONE_MINUTE * NumberConstant.NUM_120) {
            return true
        }
        val hasAmp = RecorderDBUtil.getInstance(BaseApplication.getAppContext()).checkLocalHasAmp(playModel.playPath)
        if (!hasAmp) {
            return playModel.duration > Constants.TIME_ONE_MINUTE
        }
        return false
    }

    private fun getContainerFragment(): PlaybackContainerFragment? {
        if (mFragment == null) {
            mFragment = findFragment(TAG_FRAGMENT)
        }
        return mFragment as? PlaybackContainerFragment
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        getContainerFragment()?.getIPictureMarkDelegate()?.onNewIntent(intent)
    }

    override fun onPrivacyPolicySuccess(type: Int, pageFrom: Int?) {
        getContainerFragment()?.onPrivacyPolicySuccess(type)
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        getContainerFragment()?.getIPictureMarkDelegate()?.onSaveInstanceState(outState)
    }

    override fun onRestoreInstanceState(savedInstanceState: Bundle) {
        super.onRestoreInstanceState(savedInstanceState)
        getContainerFragment()?.getIPictureMarkDelegate()?.onRestoreInstanceState(savedInstanceState)
    }

    override fun startActivityForResult(intent: Intent, requestCode: Int) {
        getContainerFragment()?.getIPictureMarkDelegate()?.setRequestCodeX(requestCode)
        super.startActivityForResult(intent, requestCode)
    }

    override fun startActivityForResult(intent: Intent, requestCode: Int, options: Bundle?) {
        getContainerFragment()?.getIPictureMarkDelegate()?.setRequestCodeX(requestCode)
        super.startActivityForResult(intent, requestCode, options)
    }
}