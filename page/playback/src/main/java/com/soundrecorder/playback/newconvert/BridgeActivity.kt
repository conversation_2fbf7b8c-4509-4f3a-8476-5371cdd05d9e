/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert

import android.os.Bundle
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import com.soundrecorder.base.ext.dismissWhenShowing
import com.soundrecorder.base.utils.ClickUtils
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.modulerouter.SettingInterface
import com.soundrecorder.modulerouter.utils.Injector

class BridgeActivity : AppCompatActivity() {
    companion object {
        const val TAG = "BridgeActivity"
    }

    private var disableDialog: AlertDialog? = null

    private val settingApi by lazy {
        Injector.injectFactory<SettingInterface>()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        DebugUtil.i(TAG, "-------->onCreate")
        super.onCreate(savedInstanceState)
        if (ClickUtils.isQuickClick()) {
            finish()
            return
        }
        settingApi?.launchBootRegPrivacy(this) {
            disableDialog = it
        }
        finish()
    }

    override fun onDestroy() {
        super.onDestroy()
        disableDialog.dismissWhenShowing()
        disableDialog = null
    }
}