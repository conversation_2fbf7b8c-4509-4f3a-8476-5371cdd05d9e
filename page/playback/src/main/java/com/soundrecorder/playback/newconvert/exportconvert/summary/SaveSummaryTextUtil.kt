/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SaveSummaryTextUtil.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/5/29
 * * Author      : W9067780
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.exportconvert.summary

import android.content.Context
import android.media.MediaScannerConnection
import android.os.Environment
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.displayName
import kotlinx.coroutines.*
import com.soundrecorder.common.constant.Constants
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FileUtils
import com.soundrecorder.playback.newconvert.exportconvert.ExportHelper
import com.soundrecorder.playback.newconvert.exportconvert.txt.SaveToLocalCallback
import java.io.File
import java.io.FileNotFoundException
import java.io.FileOutputStream
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

object SaveSummaryTextUtil {
    private const val TAG = "SaveSummaryTextUtil"
    private const val DELAY_MILLIS = 1000L
    private const val FILE_EXTENSION_SEPARATOR = "."
    private var mCallback: SaveToLocalCallback? = null
    private var mSaveTxtToLocalAbsPath: String = ""
    private var mSaveTxtToLocalFileName: String = ""

    //显示dialog的时间，最少存在1s再消失
    private var needShowDialog = false
    private var mShowDialogDuration = 0L
    @JvmStatic
    fun saveFileToLocal(
        viewModel: ShareWithSummaryViewModel,
        callback: SaveToLocalCallback?,
        viewModelScope: CoroutineScope,
    ) {
        this.mCallback = callback
        val sourceFilePath = viewModel.getExportSummaryFilePath()
        val sourceFileType = viewModel.getExportSummaryFileType()
        DebugUtil.i(TAG, "saveFileToLocal file path:$sourceFilePath file type:$sourceFileType.")
        viewModelScope.launch(Dispatchers.IO) {
            if (!checkSourceFileValid(sourceFileType, sourceFilePath)) {
                setSaveResult(false, "SaveFileToLocal input file path or type is invalid.")
                return@launch
            }

            val exportFileSize = FileUtils.getFileSizeLong(sourceFilePath)
            if (exportFileSize >= Constants.SHOW_WAITING_DIALOG_THRESHOLD) {
                DebugUtil.w(TAG, "saveFileToLocal export file size:$exportFileSize.")
                needShowDialog = true
                //开始计时
                mShowDialogDuration = System.currentTimeMillis()
                //显示waitingDialog
                withContext(Dispatchers.Main) {
                    mCallback?.onShowSaveFileWaitingDialog()
                }
            }
            val saveExportFilePath = getSaveTxtFolderPath()
            kotlin.runCatching {
                if (checkFolderExist(saveExportFilePath)) {
                    if (copyFileToPath(sourceFilePath, saveExportFilePath)
                    ) {
                        updateMediaDatabase(BaseApplication.getAppContext(), saveExportFilePath)
                        setSaveResult(true)
                    } else {
                        setSaveResult(false, "setSaveTxtResult failed")
                    }
                } else {
                    DebugUtil.e(TAG, "checkFolderExist false >>")
                    setSaveResult(false, "checkFolderExist false")
                }
            }.onFailure {
                DebugUtil.e(TAG, "saveTxtToLocal", it)
            }
        }
    }

    @JvmStatic
    private fun checkSourceFileValid(sourceFileType: String, sourceFilePath: String): Boolean {
        if (sourceFileType.isEmpty() || sourceFilePath.isEmpty()) {
            DebugUtil.e(TAG, "checkSourceFileValid input param is empty.")
            return false
        }

        if ((sourceFileType != ExportHelper.DOC_SUFFIX) && (sourceFileType != ExportHelper.PDF_SUFFIX)
            && (sourceFileType != ExportHelper.TXT_SUFFIX)
        ) {
            DebugUtil.e(TAG, "checkSourceFileValid file type is illegal.")
            return false
        }

        val sourceFileObject = File(sourceFilePath)
        if (!sourceFileObject.exists()) {
            DebugUtil.e(TAG, "checkSourceFileValid source file is not exist.")
            return false
        }

        val checkResult = sourceFileObject.extension.let {
            if (it.isEmpty() || it != sourceFileType) {
                DebugUtil.e(TAG, "checkSourceFileValid file extension name not match $it.")
                false
            } else {
                DebugUtil.i(TAG, "checkSourceFileValid file extension name match success.")
                true
            }
        }

        return checkResult
    }

    /**
     * 统一返回保存结果
     */
    @JvmStatic
    private suspend fun setSaveResult(success: Boolean, message: String = "") {
        if (needShowDialog) {
            val delay = System.currentTimeMillis() - mShowDialogDuration
            if (delay < DELAY_MILLIS) {
                delay(DELAY_MILLIS)
            }
            needShowDialog = false
        }
        withContext(Dispatchers.Main) {
            if (success) {
                if (mSaveTxtToLocalFileName.isBlank() || mSaveTxtToLocalAbsPath.isBlank()) {
                    mCallback?.onSaveFailed(message)
                } else {
                    mCallback?.onSaveSuccess(mSaveTxtToLocalFileName, mSaveTxtToLocalAbsPath)
                }
            } else {
                mCallback?.onSaveFailed(message)
            }
        }
    }

    /**
     * 检查Documents/SoundRecordDoc/文件夹是否存在
     */
    @JvmStatic
    private fun checkFolderExist(path: String): Boolean {
        return try {
            val recorderFolder = File(path)
            if (!recorderFolder.exists()) {
                recorderFolder.mkdirs()
            }
            recorderFolder.exists()
        } catch (exception: FileNotFoundException) {
            DebugUtil.e(TAG, "fileOutputStream exception > ${exception.message}")
            false
        }
    }

    /**
     * 获取保本到本地文件真正的名称
     * 有可能saveFileName已经存在，则在后面加上"_x"，x为数字，从1开始递增
     * 例如："标准录音-2021-10-26 20-57-33"-转文本结果_1
     */
    @JvmStatic
    private fun getRealFileName(folderPath: String, saveFileName: String): String {
        val realName: String
        val fileExtension = ".txt"
        var number = 1
        var file = File(folderPath + saveFileName + fileExtension)
        if (!file.exists()) {
            return saveFileName
        }
        while (true) {
            file = File(folderPath + saveFileName + "_" + number + fileExtension)
            if (!file.exists()) {
                realName = file.nameWithoutExtension
                break
            }
            number++
        }
        DebugUtil.i(TAG, "getRealFileName > $realName")
        return realName
    }

    /**
     * 将转文本内容写入到文件
     * @param fileName 文件名称，不包含扩展名
     */
    @JvmStatic
    private fun writeFileToFolder(
        viewModel: ShareWithSummaryViewModel,
        folderPath: String,
        fileName: String
    ): Boolean {
        if (fileName.isBlank() || folderPath.isBlank()) {
            DebugUtil.e(TAG, "writeFileToFolder fullFileName is empty or blank")
            return false
        }
        var fileOutputStream: FileOutputStream? = null
        try {
            val absPath = "$folderPath$fileName.txt"
            mSaveTxtToLocalAbsPath = absPath
            mSaveTxtToLocalFileName = fileName
            mCallback?.onGetFileName(fileName, absPath)
            val txtString = viewModel.summaryContent.toString()
            fileOutputStream = FileOutputStream(absPath)
            fileOutputStream.write(txtString.toByteArray())
            fileOutputStream.close()
            return true
        } catch (exception: FileNotFoundException) {
            DebugUtil.e(TAG, "fileOutputStream exception > ${exception.message}")
        } catch (exception: IOException) {
            DebugUtil.e(TAG, "fileOutputStream exception > ${exception.message}")
        } finally {
            try {
                fileOutputStream?.close()
            } catch (exception: IOException) {
                DebugUtil.e(TAG, "close fileOutputStream exception > ${exception.message}")
            }
        }
        return false
    }

    @JvmStatic
    private fun copyFileToPath(
        sourceFilePath: String,
        targetFolderPath: String
    ): Boolean {
        if (sourceFilePath.isEmpty() || targetFolderPath.isEmpty()) {
            DebugUtil.e(TAG, "copyFileToPath source file or target file is empty.")
            return false
        }

        val sourceFile = File(sourceFilePath)
        val disPlayNameWithOutExtension = sourceFile.nameWithoutExtension.displayName()
        if (disPlayNameWithOutExtension.isNullOrEmpty()) {
            DebugUtil.e(TAG, "copyFileToPath source file display name is empty.")
            return false
        }

        val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        val targetDisplayName = disPlayNameWithOutExtension + timestamp.toString() + FILE_EXTENSION_SEPARATOR + sourceFile.extension
        DebugUtil.w(TAG, "copyFileToPath targetDisplayName:$targetDisplayName.")
        val targetFile = FileUtils.copyFileWithOutCreateFolder(sourceFile, targetFolderPath, targetDisplayName)
        if (targetFile == null || (!targetFile.exists())) {
            DebugUtil.w(TAG, "copyFileToPath create file fail.")
            return false
        }
        mSaveTxtToLocalAbsPath = targetFile.absolutePath
        mSaveTxtToLocalFileName = targetFile.nameWithoutExtension.displayName() ?: ""
        return true
    }

    @JvmStatic
    fun getSaveTxtFolderPath(): String {
        return Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS).absolutePath + File.separator +
                "SoundRecordDoc"
    }

    /**
     * 保存文件完成之后更新媒体库
     */
    @JvmStatic
    private fun updateMediaDatabase(context: Context, saveTxtFolder: String) {
        //R上更新媒体库方法,
        kotlin.runCatching {
            if (saveTxtFolder.isBlank()) {
                return
            }
            val paths = arrayOf(saveTxtFolder)
            MediaScannerConnection.scanFile(context, paths, null, null)
        }.onFailure {
            DebugUtil.e(TAG, "internalMediaScanner :$saveTxtFolder, error: ${it.message}")
        }
    }
    @JvmStatic
    fun clearCallback() {
        mCallback = null
    }
}