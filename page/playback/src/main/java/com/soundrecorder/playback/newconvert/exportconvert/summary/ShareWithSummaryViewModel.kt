/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ShareWithSummaryViewModel.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/5/29
 * * Author      : ********
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.exportconvert.summary

import android.app.Application
import android.os.Bundle
import android.os.Environment
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.share.ShareSummaryText
import com.soundrecorder.common.share.ShareTextContent
import com.soundrecorder.modulerouter.share.IShareListener
import com.soundrecorder.modulerouter.share.ShareAction
import com.soundrecorder.modulerouter.share.ShareType
import com.soundrecorder.modulerouter.summary.SummaryInterface
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.playback.newconvert.exportconvert.txt.SaveToLocalCallback
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.File

class ShareWithSummaryViewModel(application: Application) : AndroidViewModel(application),
    SaveToLocalCallback, IShareListener {
    companion object {
        const val TAG = "ShareWithSummaryViewModel"
    }

    val mSummaryDate: MutableLiveData<String> = MutableLiveData("")

    //是否需要重建waitingDialog
    private var mNeedRestoreWaitingDialog: Boolean = false

    //activity/fragment重建完成后是否需要显示snackBar
    private var mNeedShowSnackBar: Boolean = false

    //保存到本地文件的名称
    private var mSaveCallBackFileName: String = ""

    //保存到本地文件的完整路径
    private var mSaveCallBackFilePath: String = ""

    //保存到本地成功后的路径
    private var saveTxtToLocalCallback: SaveToLocalCallback? = null

    //保存到本地文件名称中的日期(2021-11-03 16-20-35)
    private var mSaveTxtTitleDate: String = ""

    //分享回调，当数据准备完成之后，开始分享
    private var shareListener: IShareListener? = null

    private var mMediaRecordId: Long = -1

    var summaryContent: String? = null

    private val shareAction by lazy {
        Injector.injectFactory<ShareAction>()
    }

    //生成摘要pdf word txt 的文件路径
    private var summaryFilePath = ""

    private var fileType = ""
    private val summaryAction by lazy {
        Injector.injectFactory<SummaryInterface>()
    }

    fun loadData() {
        viewModelScope.launch(Dispatchers.IO) {
            val context = BaseApplication.getAppContext()
            val summary = summaryAction?.getSummaryContent(context, mMediaRecordId)?.first
            summaryContent = summary?.replace("*", "")?.replace("#", "")
            mSummaryDate.postValue(summaryContent)
            DebugUtil.i(TAG, "loadData  SummaryContent ${summaryContent.isNullOrEmpty().not()}")
        }
    }

    fun initArgFromBundle(arguments: Bundle?) {
        arguments?.let {
            mMediaRecordId = it.getLong("mediaRecordId")
            summaryFilePath = it.getString("summaryFilePath", "")
            fileType = it.getString("fileType", "")
        }
    }

    /**
     * 点击底部分享按钮，弹窗系统分享弹窗
     */
    fun shareTxt(activity: FragmentActivity) {
        val textSummary = summaryContent ?: return
        DebugUtil.d(TAG, "summaryFilePath $summaryFilePath")
        shareAction?.share(
            activity, ShareTextContent(mMediaRecordId, false, summaryFilePath, 0, emptyList()),
            ShareSummaryText(textSummary), viewModelScope, this
        )
    }

    /**
     * 点击底部保存按钮，保存转文本内容到本地
     * 1.先获取
     */
    fun saveTxtToLocal() {
        if (mMediaRecordId < 0) {
            DebugUtil.e(TAG, "File not exist......")
            saveTxtToLocalCallback?.onSaveFailed("mMediaRecordId < 0 or file not exist......")
            return
        }

        SaveSummaryTextUtil.saveFileToLocal(
            this, callback = saveTxtToLocalCallback, viewModelScope = viewModelScope
        )
    }


    /**
     * 设置是否需要重建waitingDialog
     */
    fun setNeedRestoreDialog(needShow: Boolean) {
        mNeedRestoreWaitingDialog = needShow
    }

    fun setOnSaveTxtToLocalResultCallback(callback: SaveToLocalCallback?) {
        saveTxtToLocalCallback = callback
    }

    fun setOnShareTxtCallbackResultCallback(listener: IShareListener?) {
        shareListener = listener
    }

    /**
     * 已经显示了snackbar
     */
    fun setAlreadyShowSnackBar() {
        mNeedShowSnackBar = false
    }

    /**
     * 保存TXT到本地需要显示waitingDialog
     */
    override fun onShowSaveFileWaitingDialog() {
        DebugUtil.i(TAG, "onShowShareWaitingDialog...")
        saveTxtToLocalCallback?.onShowSaveFileWaitingDialog()
    }

    /**
     * 保存txt到本地，获取到了保存文件的名称和路径
     */
    override fun onGetFileName(fileName: String, fileAbsPath: String) {
        mSaveCallBackFileName = fileName
        mSaveCallBackFilePath = fileAbsPath
        saveTxtToLocalCallback?.onGetFileName(fileName, fileAbsPath)
    }

    /**
     * 保存成功后，此刻可能activity正在重建，需要先保存状态值和数据，等到重建完成之后，再判断是否需要显示snackBar
     */
    override fun onSaveSuccess(fileName: String, fileAbsPath: String) {
        mNeedShowSnackBar = true
        //保存成功则不需要重建waitingDialog，直接展示snackBar
        mNeedRestoreWaitingDialog = false
        saveTxtToLocalCallback?.onSaveSuccess(fileName, fileAbsPath)
    }

    /**
     * 保存txt到本地失败
     */
    override fun onSaveFailed(message: String) {
        mNeedShowSnackBar = false
        mNeedRestoreWaitingDialog = false
        saveTxtToLocalCallback?.onSaveFailed(message)
    }

    /**
     * 分享前置流程完成
     */
    override fun onShowShareWaitingDialog(mediaId: Long, type: ShareType) {
        shareListener?.onShowShareWaitingDialog(mediaId, type)
    }

    /**
     * 分享前置流程失败
     */
    override fun onShareSuccess(mediaId: Long, type: ShareType) {
        mNeedRestoreWaitingDialog = false
        shareListener?.onShareSuccess(mediaId, type)
    }

    /**
     * 需要显示waitingDialog
     */
    override fun onShareFailed(mediaId: Long, type: ShareType, error: Int, message: String) {
        shareListener?.onShareFailed(mediaId, type, error, message)
    }

    fun getExportSummaryFilePath(): String {
        return summaryFilePath
    }

    fun getExportSummaryFileType(): String {
        return fileType
    }
}