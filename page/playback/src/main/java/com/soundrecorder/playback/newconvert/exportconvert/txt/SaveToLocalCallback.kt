/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.exportconvert.txt

/**
 * 保存txt到本地的回调
 */
interface SaveToLocalCallback {

    /**
     * 需要展示 waitingDialog
     */
    fun onShowSaveFileWaitingDialog()

    /**
     * @param fileName 需要保存的文件名（没有扩展名）
     * @param fileAbsPath 需要保存到的文件路径
     */
    fun onGetFileName(fileName: String, fileAbsPath: String)

    /**
     * 保存成功回调
     */
    fun onSaveSuccess(fileName: String, fileAbsPath: String)

    /**
     * 保存失败
     */
    fun onSaveFailed(message: String)
}