package oplus.multimedia.soundrecorder.playback.mute

import android.content.Context
import com.soundrecorder.base.utils.DebugUtil

/**
 * 静音片段数据管理
 */
object MuteCacheManager : IMuteCache {

    private const val TAG = "MuteCacheManager"

    private var sHasClearDirtyData = false

    override fun save(fullPath: String, mediaId: Long, lastModify: Long, dataList: List<MuteItem>) {
        val fileName = MuteUtil.genMuteFileName(fullPath, mediaId, lastModify)
        MuteFileUtil.writeMuteFile(fileName, dataList)
    }

    override fun load(mediaId: Long, lastModify: Long): List<MuteItem>? {
        val file = MuteUtil.matchFile(mediaId, lastModify)
        file?.let {
            return MuteFileUtil.readMuteFile(it)
        }
        return null
    }

    override fun delete(mediaId: Long) {
        MuteUtil.matchFile(mediaId)?.let {
            MuteFileUtil.delete(it)
        }
    }

    override fun delete(filePath: String) {
        MuteUtil.matchFile(filePath)?.let {
            MuteFileUtil.delete(it)
        }
    }

    override fun clearDirtyData(context: Context) {
        if (sHasClearDirtyData) {
            DebugUtil.d(TAG, "clearDirtyData, no need to clear dirty data")
            return
        }
        sHasClearDirtyData = true

        val suffixMap = MuteUtil.getSuffixMap()
        if (suffixMap.isEmpty()) {
            return
        }
        suffixMap.forEach {
            val isInvalid = MuteUtil.isMuteFileInvalid(context, it.key)
            if (isInvalid) {
                val isSuccess = MuteFileUtil.delete(it.value)
                DebugUtil.d(TAG, "clearDirtyData, file is deleted successfully: $isSuccess")
            }
        }
    }

    override fun release() {
    }
}