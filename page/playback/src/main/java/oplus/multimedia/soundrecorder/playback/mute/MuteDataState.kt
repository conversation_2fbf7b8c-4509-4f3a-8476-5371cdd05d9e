package oplus.multimedia.soundrecorder.playback.mute

import androidx.lifecycle.MutableLiveData
import oplus.multimedia.soundrecorder.playback.mute.detector.MuteDataDetectorWorker
import java.util.concurrent.ConcurrentHashMap

/*********************************************************************
 * * Copyright (C), 2021, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Version     : 1.0
 * * Date        : 2021/12/7
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
object MuteDataState {

    private const val TAG = "MuteDataState"

    const val MUTE_LOAD_STATE_INIT = 0
    const val MUTE_LOAD_STATE_PREPARING = 1
    const val MUTE_LOAD_STATE_LOADING_FROM_CACHE = 2
    const val MUTE_LOAD_STATE_LOADING_FROM_ORIGIN = 3
    const val MUTE_LOAD_STATE_COMPLETED = 4

    @Volatile
    private var loadStateCollection = ConcurrentHashMap<Int, MutableLiveData<Int>>()

    fun addLoadState(taskId: Int) {
        loadStateCollection[taskId] = MutableLiveData(MUTE_LOAD_STATE_INIT)
    }

    fun removeLoadState(taskId: Int) {
        loadStateCollection.remove(taskId)
    }

    fun needLoadData(taskId: Int): Boolean {
        return loadStateCollection[taskId]?.value != MUTE_LOAD_STATE_INIT
    }

    @Synchronized
    fun setLoadDataState(taskId: Int, loadState: Int) {
        loadStateCollection[taskId]?.postValue(loadState)
    }

    /**
     * 同时只能有一个后台检测
     */
    fun canLoadDataFromOrigin(): Boolean {
        //录制完成后预加载静音数据，cancel后台预加载
        MuteDataDetectorWorker.cancel()
        return !isLoadDataFromOrigin()
    }
    /**
     * 是否在源端检测静音
     */
    fun isLoadDataFromOrigin(): Boolean {
        loadStateCollection.forEach {
            if (it.value.value == MUTE_LOAD_STATE_LOADING_FROM_ORIGIN) {
                return true
            }
        }

        return false
    }

    fun needCancel(taskId: Int): Boolean {
        return loadStateCollection[taskId]?.value != MUTE_LOAD_STATE_COMPLETED
    }

    fun getPageMuteState(taskId: Int): Int {
        return loadStateCollection[taskId]?.value ?: MUTE_LOAD_STATE_INIT
    }

    fun getLoadingState(taskId: Int): MutableLiveData<Int>? {
        return loadStateCollection[taskId]
    }
}