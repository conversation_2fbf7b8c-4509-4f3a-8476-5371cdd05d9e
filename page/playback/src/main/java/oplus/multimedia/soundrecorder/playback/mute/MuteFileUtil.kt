package oplus.multimedia.soundrecorder.playback.mute

import android.os.SystemClock
import com.soundrecorder.base.utils.DebugUtil
import java.io.*
import java.nio.file.Files
import java.nio.file.Paths

object MuteFileUtil {

    private const val TAG = "MuteFileUtil"

    @Throws(IOException::class)
    @Synchronized
    fun writeMuteFile(fileName: String, dataList: List<MuteItem>) {
        val startTime = SystemClock.elapsedRealtime()
        try {
            val file = checkOrCreateFile(fileName)
            DebugUtil.d(TAG, "writeMuteFile, filename: ${file.name} dataList:$dataList")

            ObjectOutputStream(FileOutputStream(file)).use {
                it.writeObject(dataList)
                it.flush()
            }
        } catch (e: Exception) {
            DebugUtil.e(TAG, "writeMuteFile exception:", e)
        }
        val endTime = SystemClock.elapsedRealtime()
        DebugUtil.d(TAG, "writeMuteFile, size:${dataList.size}, time:${(endTime - startTime)}")
    }

    @Throws(IOException::class)
    @Synchronized
    fun readMuteFile(file: File): List<MuteItem>? {
        val startTime = SystemClock.elapsedRealtime()
        DebugUtil.d(TAG, "readMuteFile, filename: " + file.name)
        var list: List<MuteItem>? = null
        try {
            ObjectInputStream(FileInputStream(file)).use {
                list = it.readObject() as? List<MuteItem> ?: return null
            }
        } catch (e: Exception) {
            DebugUtil.e(TAG, "readMuteFile exception:", e)
        }
        val endTime = SystemClock.elapsedRealtime()
        DebugUtil.d(TAG, "readMuteFile, size:${list?.size}, time:${(endTime - startTime)}")
        return list
    }

    @Throws(IOException::class)
    fun checkOrCreateFile(filename: String): File {
        val file = File(MuteUtil.getMutePath(), filename)
        if (!file.exists()) {
            file.parentFile?.let {
                if (!it.exists()) {
                    val dirMakeSuc = it.mkdir()
                    DebugUtil.i(TAG, "parentDir : $it, mkdir success: $dirMakeSuc")
                    if (!dirMakeSuc) {
                        throw IOException("$filename make file path failed!")
                    }
                }
            }
            if (!file.createNewFile()) {
                throw IOException("$filename make file failed!")
            }
        }
        return file
    }

    fun delete(file: File): Boolean {
        var isSuccess = false
        if (file.exists() && file.isFile) {
            isSuccess = file.delete()
        }
        DebugUtil.d(TAG, "$file is deleted successfully:$isSuccess")
        return isSuccess
    }

    @Throws(IOException::class)
    fun delete(filepath: String) {
        val path = Paths.get(filepath)
        Files.delete(path)
    }
}