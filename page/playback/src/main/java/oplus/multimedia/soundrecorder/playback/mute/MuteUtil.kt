package oplus.multimedia.soundrecorder.playback.mute

import android.content.ContentResolver
import android.content.Context
import android.media.MediaFormat
import android.net.Uri
import android.os.ParcelFileDescriptor
import android.provider.MediaStore
import android.util.Log
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.MD5Utils
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.db.MediaDBUtils
import java.io.File
import java.io.FileDescriptor
import java.io.IOException

object MuteUtil {

    private const val TAG = "MuteUtil"
    private const val DELIMITER = "_"

    @JvmStatic
    fun getMutePath(): String {
        val path = BaseApplication.getAppContext().filesDir.toString() + MuteConstants.MUTE_PATH
        DebugUtil.d(TAG, "getMutePath: $path")
        return path
    }

    @JvmStatic
    fun genMuteFileName(fullPath: String, mediaId: Long, lastModify: Long): String {
        val md5 = MD5Utils.calcMd5(fullPath)
        val name = md5 + DELIMITER + mediaId + DELIMITER + lastModify
        DebugUtil.d(TAG, "genMuteFileName, filename is $name")
        return name
    }

    /**
     * match the file by id_lastModify
     */
    @JvmStatic
    fun matchFile(mediaId: Long, lastModified: Long): File? {
        val curSuffix: String = mediaId.toString() + DELIMITER + lastModified
        val muteFiles = getMuteFiles(getMutePath())
        muteFiles?.let { files ->
            val destFile = filterFile(curSuffix, files)
            DebugUtil.d(TAG, "match file by id_lastModify successfully, destFile:$destFile")
            return destFile
        }
        DebugUtil.d(TAG, "match file by id_lastModify failed")
        return null
    }

    /**
     * match the file by filepath
     */
    @JvmStatic
    fun matchFile(filepath: String): File? {
        val md5 = MD5Utils.calcMd5(filepath)
        DebugUtil.d(TAG, "md5 is $md5")
        val muteFiles = getMuteFiles(getMutePath())
        muteFiles?.forEach {
            val filename = it.name
            val split = filename.split(DELIMITER)
            if (split.size >= 3) {
                val prefix = split[0]
                if (md5.equals(prefix)) {
                    DebugUtil.d(TAG, "match file by path successfully, prefix is $prefix, file is $it")
                    return it
                }
            }
        }
        DebugUtil.d(TAG, "match file by path failed")
        return null
    }

    /**
     * match the file by mediaId
     */
    @JvmStatic
    fun matchFile(mediaId: Long): File? {
        val muteFiles = getMuteFiles(getMutePath())
        muteFiles?.forEach {
            if (it.name.contains(mediaId.toString())) {
                DebugUtil.d(TAG, "match file by mediaId successfully, file is $it")
                return it
            }
        }
        DebugUtil.d(TAG, "match file by mediaId failed")
        return null
    }

    /**
     * @return Map<id_lastModify,File>
     */
    @JvmStatic
    fun getSuffixMap(): Map<String, File> {
        val muteFiles = getMuteFiles(getMutePath())
        val map = mutableMapOf<String, File>()
        muteFiles?.forEach {
            val split = it.name.split(DELIMITER)
            if (split.size >= 3) {
                val suffix = split[1] + DELIMITER + split[2]
                map[suffix] = it
            }
        }
        return map
    }

    /**
     * 根据[suffix]去媒体库中查询，若查不到对应的值，则该静音数据已失效，需要删除
     */
    @JvmStatic
    fun isMuteFileInvalid(context: Context, suffix: String): Boolean {
        val split = suffix.split(DELIMITER)
        val mediaId = split[0]
        val lastModify = split[1]
        val baseUri = MediaDBUtils.BASE_URI
        val projection = arrayOf(MediaStore.Audio.Media._ID, MediaStore.Audio.Media.DATE_MODIFIED)
        val where = "${MediaStore.Audio.Media._ID} = ? AND ${MediaStore.Audio.Media.DATE_MODIFIED} = ?"
        val whereArgs = arrayOf(mediaId, lastModify)
        var count = -1
        try {
            val cursor = context.contentResolver.query(baseUri, projection, where, whereArgs, null)
            DebugUtil.d(TAG, "isMuteFileInvalid, column count is ${cursor?.count} suffix:${mediaId + DELIMITER + lastModify}")
            cursor?.use {
                count = it.count
            }
        } catch (e: Exception) {
            DebugUtil.e(TAG, "getLastModify exception", e)
        }
        return count <= 0
    }

    @JvmStatic
    fun getString(mediaFormat: MediaFormat, key: String, defaultValue: String): String? {
        var result: String? = defaultValue
        try {
            result = mediaFormat.getString(key)
        } catch (e: Exception) {
            DebugUtil.e(TAG, "getString $key error", e)
        }
        DebugUtil.i(TAG, "getString KEY $key, VALUE $result")
        return result ?: defaultValue
    }

    @JvmStatic
    fun getInteger(mediaFormat: MediaFormat, key: String, defaultValue: Int): Int {
        var result = defaultValue
        try {
            result = mediaFormat.getInteger(key)
        } catch (e: Exception) {
            DebugUtil.e(TAG, "getInteger $key error", e)
        }
        DebugUtil.i(TAG, "getInteger KEY $key, VALUE $result")
        return result
    }

    @JvmStatic
    fun getFileDescriptor(context: Context, uri: Uri?, mode: String?): FileDescriptor? {
        val resolver: ContentResolver = context.getContentResolver()
        var fd: FileDescriptor? = null
        var pfd: ParcelFileDescriptor? = null
        try {
            pfd = resolver.openFileDescriptor(uri!!, mode!!)
            if (pfd != null) {
                fd = pfd.fileDescriptor
            }
        } catch (e: Exception) {
            DebugUtil.e(TAG, "open uri get file descriptor error", e)
            try {
                pfd?.close()
            } catch (ioe: IOException) {
                Log.e(TAG, "pfd close error", ioe)
            }
        }
        return fd
    }

    /**
     * traverse files
     */
    private fun getMuteFiles(path: String): List<File>? {
        val file = File(path)
        val listFiles = file.listFiles()?.asList()
        DebugUtil.d(TAG, "all files:$listFiles")
        return listFiles
    }

    private fun getFilenameSuffix(filename: String): String {
        if (filename.isNotEmpty()) {
            val splitList = filename.split(DELIMITER)
            if (splitList.size >= 3) {
                val suffix = splitList[1] + DELIMITER + splitList[2]
                DebugUtil.d(TAG, "suffix:$suffix")
                return suffix
            }
        }
        return ""
    }

    private fun filterFile(curName: String, files: List<File>): File? {
        files.forEach {
            if (!it.isDirectory) {
                val suffix = getFilenameSuffix(it.name)
                if (suffix.isNotEmpty() && curName == suffix) {
                    return it
                }
            }
        }
        return null
    }
}