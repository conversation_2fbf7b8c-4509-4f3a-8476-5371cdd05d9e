/*********************************************************************
 * * Copyright (C), 2021, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Version     : 1.0
 * * Date        : 2021/12/29
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package oplus.multimedia.soundrecorder.playback.mute.detector

import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.db.MediaDBUtils
import kotlinx.coroutines.*
import oplus.multimedia.soundrecorder.playback.mute.IExtractFormatCallback
import oplus.multimedia.soundrecorder.playback.mute.MuteCacheManager
import oplus.multimedia.soundrecorder.playback.mute.MuteConstants.MUTE_DETECT_DURATION
import oplus.multimedia.soundrecorder.playback.mute.MuteConstants.MUTE_DETECT_WORKER
import oplus.multimedia.soundrecorder.playback.mute.MuteDataState

/**
 * 由于静音检测过程需要将音频解析成PCM流，这个过程较为耗时，因此利用workerManager后台解析静音数据并缓存
 * 为了电流和内存，要求不同时检测
 * 目前检测的时机：
 * 1、首次进入播放详情页
 * 2、刚录制完成音频
 */
class MuteDataDetectorWorker private constructor(private val fullPath: String?) {

    private val coroutineScope by lazy {
        CoroutineScope(Dispatchers.IO)
    }
    private var muteDataDetector: IMuteDataDetector? = null

    private var mediaId: Long = -1
    private var lastModify: Long = -1

    companion object {
        private const val TAG = "MuteDataDetectorWorker"
        private var sCurWorker: MuteDataDetectorWorker? = null
        val WORKER_TASK_ID = MUTE_DETECT_WORKER.hashCode()

        @Synchronized
        fun startMuteDetectIfNecessary(fullPath: String?) {
            if (fullPath.isNullOrEmpty()) {
                DebugUtil.d(TAG, "startMuteDetectIfNecessary: fullPath is invalid")
                return
            }

            if (sCurWorker != null) {
                DebugUtil.d(
                    TAG,
                    "startMuteDetectIfNecessary: sCurWorker != null, someOne is loading"
                )
                return
            }

            if (MuteDataState.isLoadDataFromOrigin()) {
                DebugUtil.d(
                    TAG,
                    "startMuteDetectIfNecessary: isLoadDataFromOrigin is true, someOne is loading"
                )
                return
            }

            startMuteDetect(fullPath)
        }

        private fun startMuteDetect(fullPath: String) {
            sCurWorker = MuteDataDetectorWorker(fullPath).also {
                it.doWork()
            }
        }

        fun cancel() {
            sCurWorker?.cancel()
            sCurWorker = null
        }
    }

    fun doWork() {
        coroutineScope.launch {
            DebugUtil.d(TAG, "doWork start")
            MuteDataState.addLoadState(WORKER_TASK_ID)
            if (checkInputData()) {
                loadMuteData()
                DebugUtil.d(TAG, "doWork success")
            }

            release()
            DebugUtil.d(TAG, "doWork end")
        }
    }

    private fun checkInputData(): Boolean {
        DebugUtil.d(TAG, "checkInputData: fullPath is $fullPath")
        MuteDataState.setLoadDataState(
            WORKER_TASK_ID,
            MuteDataState.MUTE_LOAD_STATE_PREPARING
        )
        if (fullPath.isNullOrEmpty()) {
            DebugUtil.d(TAG, "initInputData: filePath is null")
            return false
        }
        var duration = 0L
        MediaDBUtils.queryRecordByFullPath(fullPath)
            ?.let {
                mediaId = it.id
                lastModify = it.dateModied
                duration = it.duration
            }

        DebugUtil.i(TAG, "checkInputData duration: $duration")
        if (duration < MUTE_DETECT_DURATION) {
            DebugUtil.d(TAG, "startMuteDetectIfNecessary: duration is $duration, no need to load in advance")
            return false
        }
        return mediaId >= 0 && lastModify >= 0
    }

    private suspend fun loadMuteData() {
        DebugUtil.d(
            TAG,
            "loadMuteData: mediaId is $mediaId, fullPath is $fullPath, lastModify is $lastModify"
        )
        MuteDataState.setLoadDataState(
            WORKER_TASK_ID,
            MuteDataState.MUTE_LOAD_STATE_LOADING_FROM_ORIGIN
        )
        muteDataDetector = MuteDataDetectorFactory.createDetector()
        val data = muteDataDetector!!.load(
            BaseApplication.getAppContext(), mediaId,
            object : IExtractFormatCallback {
                override fun onError(errorCode: Int) {
                    DebugUtil.e(TAG, "loadMuteData error is $errorCode")
                }
            })

        if (data == null) {
            MuteDataState.setLoadDataState(WORKER_TASK_ID, MuteDataState.MUTE_LOAD_STATE_INIT)
        } else {
            MuteDataState.setLoadDataState(WORKER_TASK_ID, MuteDataState.MUTE_LOAD_STATE_COMPLETED)
            MuteCacheManager.save(fullPath!!, mediaId, lastModify, data)
        }
    }

    @Synchronized
    private fun release() {
        releaseMuteDetector()
        MuteDataState.removeLoadState(WORKER_TASK_ID)
        sCurWorker = null
    }

    private fun releaseMuteDetector() {
        if (MuteDataState.needCancel(WORKER_TASK_ID)) {
            muteDataDetector?.cancel()
        }

        muteDataDetector?.release()
        muteDataDetector = null

        try {
            coroutineScope.cancel()
        } catch (e: Exception) {
            DebugUtil.e(TAG, "releaseMuteDetector, cancel coroutine scope", e)
        }
    }

   fun cancel() {
        DebugUtil.d(TAG, "cancel")
        release()
    }
}