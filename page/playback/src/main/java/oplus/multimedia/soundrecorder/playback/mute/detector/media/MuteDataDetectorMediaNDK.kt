/*********************************************************************
 * * Copyright (C), 2021, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Version     : 1.0
 * * Date        : 2021/12/8
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package oplus.multimedia.soundrecorder.playback.mute.detector.media

class MuteDataDetectorMediaNDK {

    init {
        System.loadLibrary("silenceWrapper")
    }

    external fun silenceDetectCreate(sampleRate: Int, bits: Int, frameTimes: Int, channel: Int)

    external fun silenceDetectSetParam(maxMuteDuration: Short, maxMuteDb: Short, mutationDuration: Short, openFlag: Short)

    external fun silenceDetectProcess(data: ShortArray, dataLen: Short, EndFlag: Short): LongArray

    external fun silenceDetectRelease()
}