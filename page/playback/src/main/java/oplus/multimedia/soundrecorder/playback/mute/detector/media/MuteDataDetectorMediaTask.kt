/*********************************************************************
 * * Copyright (C), 2021, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Version     : 1.0
 * * Date        : 2021/12/15
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package oplus.multimedia.soundrecorder.playback.mute.detector.media

import android.media.MediaFormat
import com.soundrecorder.base.utils.DebugUtil
import oplus.multimedia.soundrecorder.playback.mute.MuteConstants.MEDIA_FRAME_TIMES
import oplus.multimedia.soundrecorder.playback.mute.MuteItem
import oplus.multimedia.soundrecorder.playback.mute.MuteUtil
import java.util.*

class MuteDataDetectorMediaTask(mediaFormat: MediaFormat) {

    companion object {
        const val TAG = "MuteDataDetectorMediaTask"
    }

    //音频实际声道数
    private val mOriginChannel: Int
    //10ms的pcm流占用的short数组size
    private val mFrameLen: Int

    @Volatile
    private var mSampleData: MuteMediaSampleData? = null
    private var mMuteDetectNDK: MuteDataDetectorMediaNDK? = null
    var mMuteData: MutableList<MuteItem>? = null
    private set

    var mOnTaskEnd: (() -> Unit)? = null

    init {
        val sampleRate = MuteUtil.getInteger(mediaFormat, MediaFormat.KEY_SAMPLE_RATE, -1)
//        val bits = MediaFormatUtil.getInteger(mediaFormat, MediaFormat.KEY_PCM_ENCODING, -1)
        val bits = 16
        mOriginChannel = MuteUtil.getInteger(mediaFormat, MediaFormat.KEY_CHANNEL_COUNT, 1)
        //10ms采样点占用字节
        mFrameLen = MEDIA_FRAME_TIMES * sampleRate / 1000

        /**
         * 初始化静音检测参数
         * 要求：1）单声道传输，多声道抛弃其他只剩一个；
         * 2）每次传输数据10ms大小的pcm流，对应的数据Short大小len = mFrameLen
         */
        mSampleData = MuteMediaSampleData(mFrameLen)
        mMuteDetectNDK = MuteDataDetectorMediaNDK().also {
            //强制改成1，双声道数据则只保留一个声道数据传输过去，其他的丢弃
            val channel = 1
            it.silenceDetectCreate(sampleRate, bits, MEDIA_FRAME_TIMES, channel)
//            it.silenceDetectSetParam(THRESHOLD_TIME_SECOND.toShort(), THRESHOLD_DB.toShort(),
//                SENSITIVE_TIME.toShort(), 1)
        }

        mMuteData = mutableListOf()
    }

    fun addTask(data: ByteArray, isEnd: Boolean) {
        startTaskInner(data, isEnd)
    }

    fun onTaskRelease() {
        mSampleData?.release()
        mSampleData = null

        mMuteDetectNDK?.silenceDetectRelease()
        mMuteDetectNDK = null

        mOnTaskEnd = null
    }

    @Synchronized
    private fun startTaskInner(data: ByteArray, isEnd: Boolean) {
        //某些情况特殊，最后一次的数据直接为0
        if (data.isEmpty() && isEnd) {
            onBufferFull(ShortArray(mFrameLen), 1)
            return
        }
        mSampleData?.let {
            //过滤数据，主要包括ByteArray转ShortArray&双声道转单声道
            val shortData = it.filterSampleData(mOriginChannel, data)
            //当前buffer中全量数据截断成N个10ms对应的数据，并灌入so中获取静音数据
            it.extractSampleData(shortData, isEnd) { data, endFlag ->
                onBufferFull(data, endFlag)
            }
        }
    }

    private fun onBufferFull(data: ShortArray, endFlag: Short) {
        val result = mMuteDetectNDK?.silenceDetectProcess(data, data.size.toShort(), endFlag)
        if ((result?.size ?: 0) >= 2) {
            //这里多媒体测返回的是10ms数组的下标，并不是真实的时间
            insertMuteData(result!![0] * MEDIA_FRAME_TIMES, result[1] * MEDIA_FRAME_TIMES)
        }

        if (endFlag == 1.toShort()) {
            mOnTaskEnd?.invoke()
        }
    }

    /**
     * 二分查找插入新数据，保证最终的数据按照startTime升序排列
     * 注：静音数据特殊，每个静音片段是一段段的，不会出现相邻片段时间交叠
     */
    fun insertMuteData(startTime: Long, endTime: Long) {
        DebugUtil.d(TAG, "insertMuteData, data is ($startTime, $endTime)")
        mMuteData?.let {
            val newItem = MuteItem(startTime, endTime)
            //二分查找该位置
            val pos = Collections.binarySearch(it, newItem, { o1, o2 ->
                when {
                    ((o1.startTime in o2.startTime..o2.endTime) ||
                            (o2.startTime in o1.startTime..o1.endTime)) -> {
                        //数据有交叉或者更新，本不应该出现的情况，此处仅仅采用最新的
                        0
                    }
                    (o1.startTime > o2.endTime) -> 1
                    else -> -1
                }
            })
            //pos为负，则真实pos = -1 * (pos + 1)
            if (pos >= 0) {
                it.set(pos, newItem)
            } else {
                val realPos = -1 * (pos + 1)
                if (realPos >= it.size) {
                    it.add(newItem)
                } else {
                    it.add(realPos, newItem)
                }
            }
        }
    }
}