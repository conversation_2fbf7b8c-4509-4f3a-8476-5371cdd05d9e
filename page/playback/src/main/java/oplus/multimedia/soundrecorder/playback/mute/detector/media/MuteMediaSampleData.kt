/*********************************************************************
 * * Copyright (C), 2021, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Version     : 1.0
 * * Date        : 2021/12/13
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package oplus.multimedia.soundrecorder.playback.mute.detector.media

import com.soundrecorder.base.utils.DebugUtil
import oplus.multimedia.soundrecorder.playback.mute.MuteConstants.CHANNEL_COUNT_2
import oplus.multimedia.soundrecorder.playback.mute.MuteConstants.NUM_2
import java.nio.ByteBuffer
import java.nio.ByteOrder
import kotlin.math.min


/**
 *  多媒体获取静音数据时，将原pcm流数据切成10ms大小的short数组，并传递给so
 *  1、最终结果不是10ms数组的整数倍，最后剩余的元素需要和下次的数据一起发出
 *  2、长度本身不足10ms
 */
class MuteMediaSampleData(private val frameLen: Int) {

    companion object {
        private const val TAG = "MuteMediaSampleData"
    }

    //从shortArray中获取数据的开始下标
    private var startIndex = -1
    //从shortArray中获取数据的结束下标
    private var endIndex = -1
    //存储到10ms对应的array时，从该位置往后拷贝
    private var destOffset = -1

    private var destineDataArray: ShortArray? = null

    /**
     * 过滤采样数据
     * 1、单声道数据：简单将ByteArray转成shortArray
     * 2、双声道数据：抛弃双声道数据&转成shortArray
     */
    fun filterSampleData(channel: Int, chunkPCM: ByteArray): ShortArray {
        val shortBuffer = ByteBuffer.wrap(chunkPCM).order(ByteOrder.LITTLE_ENDIAN).asShortBuffer()

        return when (channel) {
            CHANNEL_COUNT_2 -> {
                val bufferSize = shortBuffer.capacity() / NUM_2
                val leftChannelData = ShortArray(bufferSize)
                for (index in 0 until bufferSize) {
                    leftChannelData[index] = shortBuffer.get(index * NUM_2)
                }
                leftChannelData
            }
            else -> {
                return ShortArray(chunkPCM.size / NUM_2).also {
                    shortBuffer.get(it)
                }
            }
        }
    }

    /**
     * 切分采样数据
     * 1、根据之前是否遗漏未传输的数据，初始化新的dest数组
     * 2、将新数据切分到dest数组中
     */
    @Synchronized
    fun extractSampleData(shortData: ShortArray, isEnd: Boolean, function: ((ShortArray, Short) -> Unit)?) {
        if (frameLen <= 0) {
            return
        }

        initNewDestineArray(shortData.size)
        dealNewDataArray(shortData, isEnd, function)
    }

    /**
     * 初始化10ms的内容数据shortArray
     * 若上一个buffer中数据尚未处理完，则将其作为数据的头
     */
    @Synchronized
    private fun initNewDestineArray(newDataSize: Int) {
        if (destineDataArray == null) {
            destOffset = 0
            startIndex = 0
            endIndex = min(newDataSize, startIndex + frameLen) - 1

            destineDataArray = ShortArray(frameLen)
        } else {
            startIndex = 0
            if (destOffset < 0) {
                destOffset = 0
            }
            endIndex = min(newDataSize, frameLen - destOffset) - 1
        }
    }

    @Synchronized
    private fun dealNewDataArray(shortData: ShortArray, isEnd: Boolean, function: ((ShortArray, Short) -> Unit)?) {
        val newDataSize = shortData.size
        while (startIndex < endIndex) {
            //将数据shortData拷贝到当前数组destineDataArray中
            try {
                shortData.copyInto(destineDataArray!!, destOffset, startIndex, endIndex + 1)
            } catch (e: Exception) {
                DebugUtil.i(TAG, "dealNewDataArray copyInfo: $e")
            }

            if (endIndex + 1 == newDataSize) {
                //最后一个item
                val lastItemLen = endIndex - startIndex
                //数组满员或者PCM流结束，直接发送给多媒体侧处理
                if ((lastItemLen + 1 == frameLen) || isEnd) {
                    if (function != null && destineDataArray != null) {
                        function.invoke(destineDataArray!!, if (isEnd) 1 else 0)
                    }
                } else {
                    //数据不满员，留待下次处理
                    destOffset = lastItemLen
                }
                break
            } else {
                destineDataArray?.let {
                    function?.invoke(it, 0)
                    it.fill(0, 0, frameLen)
                }

                destOffset = 0
                startIndex = endIndex + 1
                endIndex = min(newDataSize, startIndex + frameLen) - 1
            }
        }
    }

    fun release() {
        startIndex = -1
        endIndex = -1
        destOffset = -1
    }
}